import React, { useState, useEffect, useContext } from "react";
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Divider,
} from "@mui/material";
import AssessmentIcon from "@mui/icons-material/Assessment";
import StarIcon from "@mui/icons-material/Star";
import TrendingUpIcon from "@mui/icons-material/TrendingUp";
import PieChartIcon from "@mui/icons-material/PieChart";
import TimelineIcon from "@mui/icons-material/Timeline";

import ReportsFilter, {
  IReportsFilterData,
} from "../../components/reportsFilter/reportsFilter.component";
import RatingsVsMonthChart from "../../components/charts/ratingsVsMonthChart.component";
import ReviewsVsRepliesChart from "../../components/charts/reviewsVsRepliesChart.component";
import RatingDistributionChart from "../../components/charts/ratingDistributionChart.component";
import ReviewVolumeChart from "../../components/charts/reviewVolumeChart.component";
import PerformanceSummaryCard from "../../components/charts/performanceSummaryCard.component";
import DailyVolumeTrendChart from "../../components/charts/dailyVolumeTrendChart.component";
import ResponseRateTrendChart from "../../components/charts/responseRateTrendChart.component";
import ResponseTimeAnalysisChart from "../../components/charts/responseTimeAnalysisChart.component";
import RatingTrendsChart from "../../components/charts/ratingTrendsChart.component";
import ReviewsPerformanceFilter, {
  IReviewsPerformanceFilterData,
} from "../../components/performanceFilter/performanceFilter.component";

import ReportsService, {
  IReviewsReportResponse,
  IPerformanceReportResponse,
} from "../../services/reports/reports.service";
import BusinessService from "../../services/business/business.service";
import { LoadingContext } from "../../context/loading.context";
import { ToastContext } from "../../context/toast.context";
import { ToastSeverity } from "../../constants/toastSeverity.constant";
import { useDispatch, useSelector } from "react-redux";
import LeftMenuComponent from "../../components/leftMenu/leftMenu.component";
import PDFExportButton from "../../components/pdfExportButton/pdfExportButton.component";
import { FilterCriteria } from "../../services/pdfExport.service";
import {
  IBusiness,
  IBusinessListResponseModel,
} from "../../interfaces/response/IBusinessListResponseModel";
import {
  IBusinessGroup,
  IBusinessGroupsResponseModel,
} from "../../interfaces/response/IBusinessGroupsResponseModel";
import {
  ILocation,
  ILocationsListResponseModel,
} from "../../interfaces/response/ILocationsListResponseModel";

interface IExtendedPageProps {
  title: string;
}

const ReviewsPerformanceReports: React.FC<IExtendedPageProps> = ({ title }) => {
  const [reportData, setReportData] = useState<
    IReviewsReportResponse["data"] | null
  >(null);
  const [performanceData, setPerformanceData] = useState<
    IPerformanceReportResponse["data"] | null
  >(null);
  const [currentFilters, setCurrentFilters] =
    useState<IReportsFilterData | null>(null);
  const [currentPerformanceFilters, setCurrentPerformanceFilters] =
    useState<IReviewsPerformanceFilterData | null>(null);

  // Add state for business lists to get actual names
  const [businessList, setBusinessList] = useState<IBusiness[]>([]);
  const [accountList, setAccountList] = useState<IBusinessGroup[]>([]);
  const [locationList, setLocationList] = useState<ILocation[]>([]);

  const { setLoading } = useContext(LoadingContext);
  const { setToastConfig } = useContext(ToastContext);
  const dispatch = useDispatch();
  const { userInfo } = useSelector((state: any) => state.authReducer);
  const _reportsService = new ReportsService(dispatch);
  const _businessService = new BusinessService(dispatch);

  useEffect(() => {
    document.title = title;
  }, [title]);

  useEffect(() => {
    fetchBusinessList();
    fetchAccountList();
    fetchLocationList();
  }, []);

  const fetchBusinessList = async () => {
    try {
      const response: IBusinessListResponseModel =
        await _businessService.getBusiness(userInfo.id);
      if (response.list.length > 0) {
        setBusinessList(response.list);
      }
    } catch (error) {
      console.error("Error fetching business list:", error);
    }
  };

  const fetchAccountList = async () => {
    try {
      const response: IBusinessGroupsResponseModel =
        await _businessService.getBusinessGroups(userInfo.id);
      if (response.data.length > 0) {
        setAccountList(response.data);
      }
    } catch (error) {
      console.error("Error fetching account list:", error);
    }
  };

  const fetchLocationList = async () => {
    try {
      const response: ILocationsListResponseModel =
        await _businessService.getLocations(userInfo.id);
      if (response.list.length > 0) {
        setLocationList(response.list);
      }
    } catch (error) {
      console.error("Error fetching location list:", error);
    }
  };

  const handlePerformanceFilterChange = async (
    filters: IReviewsPerformanceFilterData
  ) => {
    console.log("Performance filter change triggered:", filters);
    setCurrentPerformanceFilters(filters);
    await fetchPerformanceReport(filters);
  };

  const fetchPerformanceReport = async (
    filters: IReviewsPerformanceFilterData
  ) => {
    try {
      console.log("Fetching performance report with filters:", filters);
      setLoading(true);
      const response = await _reportsService.getPerformanceReport(filters);
      console.log("Performance Reports API response:", response);

      if (response.success) {
        setPerformanceData(response.data);
        console.log("Performance data set:", response.data);
      } else {
        console.error("API returned error:", response);
        setToastConfig(
          ToastSeverity.Error,
          response.message || "Failed to fetch performance data",
          true
        );
      }
    } catch (error) {
      console.error("Error fetching performance report:", error);
      setToastConfig(
        ToastSeverity.Error,
        "Failed to fetch performance data",
        true
      );
    } finally {
      setLoading(false);
    }
  };

  // Prepare filter criteria for PDF export
  const getPerformanceFilterCriteria = (): FilterCriteria => {
    if (!currentPerformanceFilters) return {};

    // Find the actual selected business, account, and location names
    const selectedBusiness = businessList.find(
      (business) =>
        business.id.toString() === currentPerformanceFilters.businessId
    );
    const selectedAccount = accountList.find(
      (account) => account.accountId === currentPerformanceFilters.accountId
    );
    const selectedLocation = locationList.find(
      (location) =>
        location.gmbLocationId === currentPerformanceFilters.locationId
    );

    return {
      businessName: selectedBusiness?.businessName || "All Businesses",
      accountName: selectedAccount?.accountName || "All Accounts",
      locationName: selectedLocation?.gmbLocationName || "All Locations",
      fromDate: currentPerformanceFilters.fromDate || "",
      toDate: currentPerformanceFilters.toDate || "",
    };
  };

  const handlePerformanceExport = async () => {
    if (!currentPerformanceFilters || !performanceData) {
      setToastConfig(
        ToastSeverity.Warning,
        "No performance data available to export. Please analyze performance first.",
        true
      );
      return;
    }

    try {
      setLoading(true);

      // Use frontend Excel export functionality for performance data
      await exportPerformanceDataToExcel();

      setToastConfig(
        ToastSeverity.Success,
        "Performance report exported successfully",
        true
      );
    } catch (error) {
      console.error("Error exporting performance report:", error);
      setToastConfig(
        ToastSeverity.Error,
        "Failed to export performance report",
        true
      );
    } finally {
      setLoading(false);
    }
  };

  const exportPerformanceDataToExcel = async () => {
    if (!performanceData) {
      throw new Error("No performance data available");
    }

    try {
      // Create workbook
      const XLSX = await import("xlsx");
      const { saveAs } = await import("file-saver");

      const workbook = XLSX.utils.book_new();

      // Prepare summary data
      const summaryData = [
        ["Performance Reviews Report Summary"],
        [],
        ["Export Date", new Date().toLocaleString()],
        ["Business", currentPerformanceFilters?.businessId || "All"],
        ["Account", currentPerformanceFilters?.accountId || "All"],
        ["Location", currentPerformanceFilters?.locationId || "All"],
        [
          "Analysis Period",
          `${performanceData.dateRange.fromDate} to ${performanceData.dateRange.toDate}`,
        ],
        [],
        ["Current Period Metrics"],
        [
          "Total Reviews",
          performanceData.performance.currentPeriod.totalReviews,
        ],
        [
          "Total Replies",
          performanceData.performance.currentPeriod.totalReplies,
        ],
        [
          "Response Rate",
          `${performanceData.performance.currentPeriod.responseRate.toFixed(
            1
          )}%`,
        ],
        [
          "Average Rating",
          performanceData.performance.currentPeriod.avgRating.toFixed(2),
        ],
        [
          "Avg Response Time (hours)",
          performanceData.performance.currentPeriod.avgResponseTime.toFixed(1),
        ],
        [],
        ["Performance Trends (vs Previous Period)"],
        [
          "Reviews Change",
          `${performanceData.performance.trends.reviewsChange.toFixed(1)}%`,
        ],
        [
          "Replies Change",
          `${performanceData.performance.trends.repliesChange.toFixed(1)}%`,
        ],
        [
          "Response Rate Change",
          `${performanceData.performance.trends.responseRateChange.toFixed(
            1
          )}%`,
        ],
        [
          "Rating Change",
          `${performanceData.performance.trends.avgRatingChange.toFixed(1)}%`,
        ],
        [
          "Response Time Change",
          `${performanceData.performance.trends.avgResponseTimeChange.toFixed(
            1
          )}%`,
        ],
      ];

      // Create summary sheet
      const summarySheet = XLSX.utils.aoa_to_sheet(summaryData);
      XLSX.utils.book_append_sheet(workbook, summarySheet, "Summary");

      // Prepare daily volume data
      const dailyVolumeData: any[][] = [["Date", "Reviews", "Replies"]];
      Object.entries(performanceData.performance.charts.dailyVolume).forEach(
        ([date, data]: [string, any]) => {
          dailyVolumeData.push([date, data.reviews, data.replies]);
        }
      );
      const dailyVolumeSheet = XLSX.utils.aoa_to_sheet(dailyVolumeData);
      XLSX.utils.book_append_sheet(workbook, dailyVolumeSheet, "Daily Volume");

      // Prepare response rate trend data
      const responseRateData: any[][] = [
        ["Week", "Total Reviews", "Replies", "Response Rate %"],
      ];
      Object.entries(
        performanceData.performance.charts.responseRateTrend
      ).forEach(([week, data]: [string, any]) => {
        responseRateData.push([
          week,
          data.total,
          data.replied,
          data.responseRate.toFixed(1),
        ]);
      });
      const responseRateSheet = XLSX.utils.aoa_to_sheet(responseRateData);
      XLSX.utils.book_append_sheet(
        workbook,
        responseRateSheet,
        "Response Rate Trend"
      );

      // Prepare response time analysis data
      const responseTimeData: any[][] = [["Time Range", "Number of Replies"]];
      Object.entries(
        performanceData.performance.charts.responseTimeAnalysis
      ).forEach(([range, count]: [string, any]) => {
        responseTimeData.push([range, count]);
      });
      const responseTimeSheet = XLSX.utils.aoa_to_sheet(responseTimeData);
      XLSX.utils.book_append_sheet(
        workbook,
        responseTimeSheet,
        "Response Time Analysis"
      );

      // Generate filename with timestamp
      const timestamp = new Date()
        .toISOString()
        .slice(0, 19)
        .replace(/:/g, "-");
      const filename = `Performance_Report_${timestamp}.xlsx`;

      // Export the file
      const excelBuffer = XLSX.write(workbook, {
        bookType: "xlsx",
        type: "array",
      });

      const blob = new Blob([excelBuffer], {
        type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      });

      // Use fallback download method
      try {
        saveAs(blob, filename);
      } catch (saveAsError) {
        console.error("saveAs failed, trying alternative method:", saveAsError);

        // Fallback download method
        const url = URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;
        link.download = filename;
        link.style.display = "none";
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
      }
    } catch (error) {
      console.error("Error in exportPerformanceDataToExcel:", error);
      throw error;
    }
  };

  return (
    <div>
      <LeftMenuComponent>
        <Box
          sx={{
            pr: 1,
          }}
        >
          <Box className="page">
            <Box className="pageContainer">
              <Box className="rightSideContainer">
                <Box className="pageContent">
                  <Box className="pageHeader" sx={{ marginBottom: "20px" }}>
                    <Typography variant="h4" className="pageTitle">
                      Google Review Trends
                    </Typography>
                    <Typography variant="body1" color="text.secondary">
                      Comprehensive reporting dashboard for business insights
                    </Typography>
                  </Box>
                  <Divider></Divider>
                  <Grid container spacing={2}>
                    {/* Right Content - Reports */}
                    <Grid item xs={12} md={12}>
                      <Box>
                        {/* Performance Filters */}
                        <ReviewsPerformanceFilter
                          onFilterChange={handlePerformanceFilterChange}
                          onExport={handlePerformanceExport}
                          showExport={performanceData ? true : false}
                          filterCriteria={getPerformanceFilterCriteria()}
                        />

                        {/* Performance Charts */}
                        {performanceData && (
                          <Box>
                            {/* Performance Summary */}
                            <PerformanceSummaryCard
                              currentPeriod={
                                performanceData.performance.currentPeriod
                              }
                              trends={performanceData.performance.trends}
                              dateRange={performanceData.dateRange}
                            />

                            {/* Charts Grid */}
                            <Grid
                              container
                              spacing={3}
                              className="performance-reports-container"
                            >
                              <Grid item xs={12} lg={6}>
                                <DailyVolumeTrendChart
                                  data={
                                    performanceData.performance.charts
                                      .dailyVolume
                                  }
                                />
                              </Grid>

                              <Grid item xs={12} lg={6}>
                                <ResponseRateTrendChart
                                  data={
                                    performanceData.performance.charts
                                      .responseRateTrend
                                  }
                                />
                              </Grid>

                              <Grid item xs={12} lg={6}>
                                <RatingTrendsChart
                                  data={
                                    performanceData.performance.charts
                                      .ratingTrends
                                  }
                                />
                              </Grid>

                              <Grid item xs={12} lg={6}>
                                <ResponseTimeAnalysisChart
                                  data={
                                    performanceData.performance.charts
                                      .responseTimeAnalysis
                                  }
                                />
                              </Grid>
                            </Grid>
                          </Box>
                        )}

                        {!performanceData && (
                          <Box
                            sx={{
                              display: "flex",
                              flexDirection: "column",
                              justifyContent: "center",
                              alignItems: "center",
                              height: 400,
                              borderRadius: 2,
                              textAlign: "center",
                              p: 3,
                            }}
                          >
                            <Typography
                              variant="h6"
                              color="text.secondary"
                              gutterBottom
                            >
                              Select Required Filters to Generate Performance
                              Analysis
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                              Please select Business, Account, Location, and
                              Date Range, then click "Analyze Performance" to
                              view your performance reports.
                            </Typography>
                          </Box>
                        )}
                      </Box>
                    </Grid>
                  </Grid>
                </Box>
              </Box>
            </Box>
          </Box>
        </Box>
      </LeftMenuComponent>
    </div>
  );
};

export default ReviewsPerformanceReports;
