{"timestamp":"2025-08-12T05:38:26.861Z","level":"INFO","message":"Instagram Service Configuration","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"hasRedirectUri":true,"clientId":"12424462...","redirectUri":"http://localhost:3000/v1/instagram/callback","apiVersion":"v20.0"}
{"timestamp":"2025-08-12T05:38:26.906Z","level":"INFO","message":"Twitter service initialized","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"redirectUri":"http://***********:5173/business-management/twitter/callback","tokenURL":"https://api.x.com/2/oauth2/token","authURL":"https://twitter.com/i/oauth2/authorize"}
{"timestamp":"2025-08-12T05:38:34.783Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-08-12T00:08:35.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-08-12T05:38:58.273Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"60a77496-b127-4a96-b78b-88cd32345691","controller":"auth","action":"login","email":"<EMAIL>"}
{"timestamp":"2025-08-12T05:38:58.286Z","level":"INFO","message":"Login attempt","environment":"DEVELOPMENT","requestId":"60a77496-b127-4a96-b78b-88cd32345691","email":"<EMAIL>","ip":"::1"}
{"timestamp":"2025-08-12T05:39:00.270Z","level":"INFO","message":"Login successful","environment":"DEVELOPMENT","requestId":"60a77496-b127-4a96-b78b-88cd32345691","userId":111,"email":"<EMAIL>"}
{"timestamp":"2025-08-12T05:39:02.766Z","level":"INFO","message":"Fetching location metrics from database","environment":"DEVELOPMENT","locationId":"11768998853595948830","accountId":"115421225674922028914","startDate":"2024-08-12","endDate":"2025-08-12"}
{"timestamp":"2025-08-12T05:39:02.952Z","level":"INFO","message":"Analytics data found in database","environment":"DEVELOPMENT","locationId":"11768998853595948830","metricsCount":7}
{"timestamp":"2025-08-12T05:39:03.331Z","level":"INFO","message":"Fetching location metrics from database","environment":"DEVELOPMENT","locationId":"11768998853595948830","accountId":"115421225674922028914","startDate":"2024-08-12","endDate":"2025-08-12"}
{"timestamp":"2025-08-12T05:39:03.645Z","level":"INFO","message":"Analytics data found in database","environment":"DEVELOPMENT","locationId":"11768998853595948830","metricsCount":7}
{"timestamp":"2025-08-12T05:39:07.063Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"af313a8a-4363-4d4e-926f-1341cf6e1879","controller":"reportScheduler","action":"getSchedulerStats"}
{"timestamp":"2025-08-12T05:39:07.072Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"10d09ebf-ac4f-44b5-9f46-f5b1bb710d99","controller":"reportScheduler","action":"getScheduledReports"}
{"timestamp":"2025-08-12T05:39:07.357Z","level":"INFO","message":"Scheduled reports retrieved successfully","environment":"DEVELOPMENT","requestId":"10d09ebf-ac4f-44b5-9f46-f5b1bb710d99","userId":111,"count":1}
{"timestamp":"2025-08-12T05:39:07.388Z","level":"INFO","message":"Retrieved SES sending statistics","environment":"DEVELOPMENT","dataPoints":18}
{"timestamp":"2025-08-12T05:39:13.398Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"e472eaff-2ff4-4e51-bd75-d1243b438c53","controller":"reportScheduler","action":"runReportNow"}
{"timestamp":"2025-08-12T05:39:13.452Z","level":"INFO","message":"Starting report execution","environment":"DEVELOPMENT","reportId":1,"isManualTrigger":true}
{"timestamp":"2025-08-12T05:39:13.526Z","level":"INFO","message":"Execution log created successfully","environment":"DEVELOPMENT","executionLogId":3,"scheduledReportId":1}
{"timestamp":"2025-08-12T05:39:13.527Z","level":"INFO","message":"Execution log created","environment":"DEVELOPMENT","reportId":1,"executionLogId":3,"reportName":"My Report - HealthOnUs - Corporate Office Beside pillar No. 1725, Phase 2, Kavuri Hills, Madhapur"}
{"timestamp":"2025-08-12T05:39:13.529Z","level":"INFO","message":"Generating report data","environment":"DEVELOPMENT","reportId":1,"reportType":"Google Review Reports","reportName":"My Report - HealthOnUs - Corporate Office Beside pillar No. 1725, Phase 2, Kavuri Hills, Madhapur"}
{"timestamp":"2025-08-12T05:39:13.610Z","level":"INFO","message":"Fetching real data for report","environment":"DEVELOPMENT","reportType":"Google Review Reports","userId":111,"filters":{},"dateRange":{"fromDate":"2025-07-13","toDate":"2025-08-12"}}
{"timestamp":"2025-08-12T05:39:13.709Z","level":"ERROR","message":"Error fetching Google Review data:","environment":"DEVELOPMENT","stack":"TypeError: ReportsModel.generateReviewsReport is not a function\n    at ReportExecutionService.fetchGoogleReviewData (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\reportExecutionService.js:640:43)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async ReportExecutionService.fetchRealDataByType (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\reportExecutionService.js:267:18)\n    at async ReportExecutionService.generateReportData (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\reportExecutionService.js:171:27)\n    at async ReportExecutionService.executeReport (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\reportExecutionService.js:56:26)\n    at async runReportNow (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\reportScheduler.controller.js:530:20)"}
{"timestamp":"2025-08-12T05:39:13.711Z","level":"INFO","message":"Report data generated successfully","environment":"DEVELOPMENT","reportId":1,"dataKeys":["reportName","reportType","dateRange","dateRangeObject","filters","generatedAt","recipientName","totalReviews","averageRating","newReviews","responseRate","averageResponseTime","ratingDistribution","recentReviews","locationPerformance","insights","dashboardUrl","nextReportDate"],"dataSize":673}
{"timestamp":"2025-08-12T05:39:13.722Z","level":"INFO","message":"Generating report attachments","environment":"DEVELOPMENT","reportType":"Google Review Reports"}
{"timestamp":"2025-08-12T05:39:13.800Z","level":"INFO","message":"Google Review Excel report generated successfully","environment":"DEVELOPMENT","fileName":"google_review_report_1754977153728.xlsx","sheets":4}
{"timestamp":"2025-08-12T05:39:13.804Z","level":"INFO","message":"Uploading file to S3","environment":"DEVELOPMENT","s3Key":"reports/2025/8/google_review_report_1754977153728.xlsx","mimeType":"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","bucketName":"gmb-social-assets"}
{"timestamp":"2025-08-12T05:39:16.674Z","level":"INFO","message":"File uploaded successfully to S3","environment":"DEVELOPMENT","s3Key":"reports/2025/8/google_review_report_1754977153728.xlsx","location":"https://gmb-social-assets.s3.amazonaws.com/reports/2025/8/google_review_report_1754977153728.xlsx","etag":"\"56bcec720a9d79a6599ee3130cb3802a\""}
{"timestamp":"2025-08-12T05:39:16.675Z","level":"INFO","message":"Report uploaded to S3 successfully","environment":"DEVELOPMENT","fileName":"google_review_report_1754977153728.xlsx","s3Key":"reports/2025/8/google_review_report_1754977153728.xlsx","url":"https://gmb-social-assets.s3.amazonaws.com/reports/2025/8/google_review_report_1754977153728.xlsx"}
{"timestamp":"2025-08-12T05:39:16.677Z","level":"INFO","message":"Complete report package generated successfully","environment":"DEVELOPMENT","reportType":"Google Review Reports","hasExcel":true,"hasPDF":false,"attachments":1}
{"timestamp":"2025-08-12T05:39:16.678Z","level":"INFO","message":"Parsing S3 URL","environment":"DEVELOPMENT","url":"https://gmb-social-assets.s3.amazonaws.com/reports/2025/8/google_review_report_1754977153728.xlsx"}
{"timestamp":"2025-08-12T05:39:16.679Z","level":"INFO","message":"Downloading file from S3","environment":"DEVELOPMENT","bucket":"gmb-social-assets","key":"reports/2025/8/google_review_report_1754977153728.xlsx","url":"https://gmb-social-assets.s3.amazonaws.com/reports/2025/8/google_review_report_1754977153728.xlsx"}
{"timestamp":"2025-08-12T05:39:19.452Z","level":"INFO","message":"Successfully downloaded file from S3","environment":"DEVELOPMENT","bucket":"gmb-social-assets","key":"reports/2025/8/google_review_report_1754977153728.xlsx","size":9138,"contentType":"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}
{"timestamp":"2025-08-12T05:39:19.454Z","level":"INFO","message":"Report attachments generated successfully","environment":"DEVELOPMENT","reportType":"Google Review Reports","attachmentCount":1}
{"timestamp":"2025-08-12T05:39:19.456Z","level":"INFO","message":"Generating email content","environment":"DEVELOPMENT","reportType":"Google Review Reports"}
{"timestamp":"2025-08-12T05:39:19.461Z","level":"INFO","message":"Base email template loaded successfully","environment":"DEVELOPMENT"}
{"timestamp":"2025-08-12T05:39:19.463Z","level":"INFO","message":"Handlebars helpers registered successfully","environment":"DEVELOPMENT"}
{"timestamp":"2025-08-12T05:39:19.465Z","level":"INFO","message":"Email template service initialized successfully","environment":"DEVELOPMENT"}
{"timestamp":"2025-08-12T05:39:19.471Z","level":"INFO","message":"Template 'googleReviewReports' loaded and compiled successfully","environment":"DEVELOPMENT"}
{"timestamp":"2025-08-12T05:39:19.515Z","level":"INFO","message":"Template 'googleReviewReports' rendered successfully","environment":"DEVELOPMENT","dataKeys":["subject","recipientName","reportName","dateRange","generatedDate","locationCount","totalReviews","averageRating","newReviews","responseRate","averageResponseTime","ratingTrend","ratingChange","newReviewsTrend","newReviewsChange","responseRateTrend","responseRateChange","responseTimeTrend","responseTimeChange","ratingDistribution","recentReviews","locationPerformance","insights","hasRecentReviews","hasAttachments","attachments","dashboardUrl","manageReviewsUrl","nextReportDate"]}
{"timestamp":"2025-08-12T05:39:19.517Z","level":"INFO","message":"Email content generated successfully","environment":"DEVELOPMENT","reportType":"Google Review Reports","htmlSize":19711}
{"timestamp":"2025-08-12T05:39:19.518Z","level":"INFO","message":"Uploading file to S3","environment":"DEVELOPMENT","s3Key":"report-attachments/2025-08-12/3/google_review_report_1754977153728.xlsx","mimeType":"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","bucketName":"gmb-social-assets"}
{"timestamp":"2025-08-12T05:39:22.331Z","level":"INFO","message":"File uploaded successfully to S3","environment":"DEVELOPMENT","s3Key":"report-attachments/2025-08-12/3/google_review_report_1754977153728.xlsx","location":"https://gmb-social-assets.s3.amazonaws.com/report-attachments/2025-08-12/3/google_review_report_1754977153728.xlsx","etag":"\"56bcec720a9d79a6599ee3130cb3802a\""}
{"timestamp":"2025-08-12T05:39:22.332Z","level":"INFO","message":"Report attachment stored successfully","environment":"DEVELOPMENT","executionLogId":3,"fileName":"google_review_report_1754977153728.xlsx","s3Key":"report-attachments/2025-08-12/3/google_review_report_1754977153728.xlsx","size":9138}
{"timestamp":"2025-08-12T05:39:22.371Z","level":"INFO","message":"Execution log updated successfully","environment":"DEVELOPMENT","executionLogId":3,"affectedRows":1}
{"timestamp":"2025-08-12T05:39:22.374Z","level":"INFO","message":"Report attachments processing completed","environment":"DEVELOPMENT","executionLogId":3,"totalAttachments":1,"successfulUploads":1}
{"timestamp":"2025-08-12T05:39:22.376Z","level":"INFO","message":"Sending report emails","environment":"DEVELOPMENT","toRecipients":1,"ccRecipients":0,"bccRecipients":0,"attachments":1}
{"timestamp":"2025-08-12T05:39:22.378Z","level":"INFO","message":"Downloading attachment from S3","environment":"DEVELOPMENT","fileName":"google_review_report_1754977153728.xlsx","url":"https://gmb-social-assets.s3.amazonaws.com/report-attachments/2025-08-12/3/google_review_report_1754977153728.xlsx"}
{"timestamp":"2025-08-12T05:39:22.379Z","level":"INFO","message":"Parsing S3 URL","environment":"DEVELOPMENT","url":"https://gmb-social-assets.s3.amazonaws.com/report-attachments/2025-08-12/3/google_review_report_1754977153728.xlsx"}
{"timestamp":"2025-08-12T05:39:22.380Z","level":"INFO","message":"Downloading file from S3","environment":"DEVELOPMENT","bucket":"gmb-social-assets","key":"report-attachments/2025-08-12/3/google_review_report_1754977153728.xlsx","url":"https://gmb-social-assets.s3.amazonaws.com/report-attachments/2025-08-12/3/google_review_report_1754977153728.xlsx"}
{"timestamp":"2025-08-12T05:39:25.381Z","level":"INFO","message":"Successfully downloaded file from S3","environment":"DEVELOPMENT","bucket":"gmb-social-assets","key":"report-attachments/2025-08-12/3/google_review_report_1754977153728.xlsx","size":9138,"contentType":"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}
{"timestamp":"2025-08-12T05:39:25.383Z","level":"INFO","message":"Successfully prepared attachment","environment":"DEVELOPMENT","fileName":"google_review_report_1754977153728.xlsx","size":9138}
{"timestamp":"2025-08-12T05:39:25.385Z","level":"INFO","message":"Prepared email attachments","environment":"DEVELOPMENT","totalAttachments":1,"attachmentSizes":[{"name":"google_review_report_1754977153728.xlsx","size":9138,"type":"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}]}
{"timestamp":"2025-08-12T05:39:25.388Z","level":"INFO","message":"Preparing to send email","environment":"DEVELOPMENT","to":1,"cc":0,"bcc":0,"subject":"Google Review Reports - My Report - HealthOnUs - Corporate Office Beside pillar No. 1725, Phase 2, K","attachments":1}
{"timestamp":"2025-08-12T05:39:25.659Z","level":"INFO","message":"Email sent successfully","environment":"DEVELOPMENT","messageIds":["010901989cca6111-a6a4dbc7-623f-4e2d-beb1-5a2140f5833a-000000"],"to":1,"cc":0,"bcc":0,"attachments":1}
{"timestamp":"2025-08-12T05:39:25.692Z","level":"INFO","message":"Email transaction logged successfully","environment":"DEVELOPMENT","transactionLogId":3,"executionLogId":3,"recipientEmail":"<EMAIL>","emailStatus":"sent"}
{"timestamp":"2025-08-12T05:39:25.693Z","level":"INFO","message":"Email transactions batch logged successfully","environment":"DEVELOPMENT","executionLogId":3,"transactionCount":1,"transactionIds":[3]}
{"timestamp":"2025-08-12T05:39:25.695Z","level":"INFO","message":"Report emails sent successfully","environment":"DEVELOPMENT","emailsSent":1,"messageIds":["010901989cca6111-a6a4dbc7-623f-4e2d-beb1-5a2140f5833a-000000"]}
{"timestamp":"2025-08-12T05:39:25.725Z","level":"INFO","message":"Execution log updated successfully","environment":"DEVELOPMENT","executionLogId":3,"affectedRows":1}
{"timestamp":"2025-08-12T05:39:25.727Z","level":"INFO","message":"Report execution completed successfully","environment":"DEVELOPMENT","reportId":1,"executionLogId":3,"emailsSent":1,"executionDuration":12245,"attachmentCount":1}
{"timestamp":"2025-08-12T05:39:25.729Z","level":"INFO","message":"Manual report execution completed","environment":"DEVELOPMENT","requestId":"e472eaff-2ff4-4e51-bd75-d1243b438c53","userId":111,"reportId":1,"executionId":3,"emailsSent":1}
{"timestamp":"2025-08-12T05:39:25.775Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"90c119d0-f48a-40b1-8ed8-0d4ee0b2b9e6","controller":"reportScheduler","action":"getScheduledReports"}
{"timestamp":"2025-08-12T05:39:25.828Z","level":"INFO","message":"Scheduled reports retrieved successfully","environment":"DEVELOPMENT","requestId":"90c119d0-f48a-40b1-8ed8-0d4ee0b2b9e6","userId":111,"count":1}
{"timestamp":"2025-08-12T06:07:56.924Z","level":"INFO","message":"Instagram Service Configuration","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"hasRedirectUri":true,"clientId":"12424462...","redirectUri":"http://localhost:3000/v1/instagram/callback","apiVersion":"v20.0"}
{"timestamp":"2025-08-12T06:07:56.957Z","level":"INFO","message":"Twitter service initialized","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"redirectUri":"http://***********:5173/business-management/twitter/callback","tokenURL":"https://api.x.com/2/oauth2/token","authURL":"https://twitter.com/i/oauth2/authorize"}
{"timestamp":"2025-08-12T06:07:58.685Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-08-12T00:37:59.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-08-12T06:08:06.547Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"53a5c588-256e-4989-91f3-8f53b564f164","controller":"reportScheduler","action":"runReportNow"}
{"timestamp":"2025-08-12T06:08:06.596Z","level":"INFO","message":"Starting report execution","environment":"DEVELOPMENT","reportId":1,"isManualTrigger":true}
{"timestamp":"2025-08-12T06:08:06.675Z","level":"INFO","message":"Execution log created successfully","environment":"DEVELOPMENT","executionLogId":5,"scheduledReportId":1}
{"timestamp":"2025-08-12T06:08:06.676Z","level":"INFO","message":"Execution log created","environment":"DEVELOPMENT","reportId":1,"executionLogId":5,"reportName":"My Report - HealthOnUs - Corporate Office Beside pillar No. 1725, Phase 2, Kavuri Hills, Madhapur"}
{"timestamp":"2025-08-12T06:08:06.678Z","level":"INFO","message":"Generating report data","environment":"DEVELOPMENT","reportId":1,"reportType":"Google Review Reports","reportName":"My Report - HealthOnUs - Corporate Office Beside pillar No. 1725, Phase 2, Kavuri Hills, Madhapur"}
{"timestamp":"2025-08-12T06:08:06.781Z","level":"INFO","message":"Fetching real data for report","environment":"DEVELOPMENT","reportType":"Google Review Reports","userId":111,"filters":{},"dateRange":{"fromDate":"2025-07-13","toDate":"2025-08-12"}}
{"timestamp":"2025-08-12T06:08:06.898Z","level":"ERROR","message":"Error fetching Google Review data:","environment":"DEVELOPMENT","stack":"TypeError: ReportsModel.generateReviewsReport is not a function\n    at ReportExecutionService.fetchGoogleReviewData (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\reportExecutionService.js:640:43)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async ReportExecutionService.fetchRealDataByType (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\reportExecutionService.js:267:18)\n    at async ReportExecutionService.generateReportData (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\reportExecutionService.js:171:27)\n    at async ReportExecutionService.executeReport (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\reportExecutionService.js:56:26)\n    at async runReportNow (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\reportScheduler.controller.js:530:20)"}
{"timestamp":"2025-08-12T06:08:06.899Z","level":"INFO","message":"Report data generated successfully","environment":"DEVELOPMENT","reportId":1,"dataKeys":["reportName","reportType","dateRange","dateRangeObject","filters","generatedAt","recipientName","totalReviews","averageRating","newReviews","responseRate","averageResponseTime","ratingDistribution","recentReviews","locationPerformance","insights","dashboardUrl","nextReportDate"],"dataSize":673}
{"timestamp":"2025-08-12T06:08:06.903Z","level":"INFO","message":"Generating report attachments","environment":"DEVELOPMENT","reportType":"Google Review Reports"}
{"timestamp":"2025-08-12T06:08:06.999Z","level":"INFO","message":"Google Review Excel report generated successfully","environment":"DEVELOPMENT","fileName":"google_review_report_1754978886910.xlsx","sheets":4}
{"timestamp":"2025-08-12T06:08:07.006Z","level":"INFO","message":"Uploading file to S3","environment":"DEVELOPMENT","s3Key":"reports/2025/8/google_review_report_1754978886910.xlsx","mimeType":"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","bucketName":"gmb-social-assets"}
{"timestamp":"2025-08-12T06:08:09.971Z","level":"INFO","message":"File uploaded successfully to S3","environment":"DEVELOPMENT","s3Key":"reports/2025/8/google_review_report_1754978886910.xlsx","location":"https://gmb-social-assets.s3.amazonaws.com/reports/2025/8/google_review_report_1754978886910.xlsx","etag":"\"da59dcd81e7a8bdc385ad7b31561b260\""}
{"timestamp":"2025-08-12T06:08:09.973Z","level":"INFO","message":"Report uploaded to S3 successfully","environment":"DEVELOPMENT","fileName":"google_review_report_1754978886910.xlsx","s3Key":"reports/2025/8/google_review_report_1754978886910.xlsx","url":"https://gmb-social-assets.s3.amazonaws.com/reports/2025/8/google_review_report_1754978886910.xlsx"}
{"timestamp":"2025-08-12T06:08:09.976Z","level":"INFO","message":"Complete report package generated successfully","environment":"DEVELOPMENT","reportType":"Google Review Reports","hasExcel":true,"hasPDF":false,"attachments":1}
{"timestamp":"2025-08-12T06:08:09.978Z","level":"INFO","message":"Parsing S3 URL","environment":"DEVELOPMENT","url":"https://gmb-social-assets.s3.amazonaws.com/reports/2025/8/google_review_report_1754978886910.xlsx"}
{"timestamp":"2025-08-12T06:08:09.981Z","level":"INFO","message":"Downloading file from S3","environment":"DEVELOPMENT","bucket":"gmb-social-assets","key":"reports/2025/8/google_review_report_1754978886910.xlsx","url":"https://gmb-social-assets.s3.amazonaws.com/reports/2025/8/google_review_report_1754978886910.xlsx"}
{"timestamp":"2025-08-12T06:08:12.799Z","level":"INFO","message":"Successfully downloaded file from S3","environment":"DEVELOPMENT","bucket":"gmb-social-assets","key":"reports/2025/8/google_review_report_1754978886910.xlsx","size":9137,"contentType":"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}
{"timestamp":"2025-08-12T06:08:12.802Z","level":"INFO","message":"Report attachments generated successfully","environment":"DEVELOPMENT","reportType":"Google Review Reports","attachmentCount":1}
{"timestamp":"2025-08-12T06:08:12.804Z","level":"INFO","message":"Generating email content","environment":"DEVELOPMENT","reportType":"Google Review Reports"}
{"timestamp":"2025-08-12T06:08:12.815Z","level":"INFO","message":"Base email template loaded successfully","environment":"DEVELOPMENT"}
{"timestamp":"2025-08-12T06:08:12.817Z","level":"INFO","message":"Handlebars helpers registered successfully","environment":"DEVELOPMENT"}
{"timestamp":"2025-08-12T06:08:12.819Z","level":"INFO","message":"Email template service initialized successfully","environment":"DEVELOPMENT"}
{"timestamp":"2025-08-12T06:08:12.825Z","level":"INFO","message":"Template 'googleReviewReports' loaded and compiled successfully","environment":"DEVELOPMENT"}
{"timestamp":"2025-08-12T06:08:12.864Z","level":"INFO","message":"Template 'googleReviewReports' rendered successfully","environment":"DEVELOPMENT","dataKeys":["subject","recipientName","reportName","dateRange","generatedDate","locationCount","totalReviews","averageRating","newReviews","responseRate","averageResponseTime","ratingTrend","ratingChange","newReviewsTrend","newReviewsChange","responseRateTrend","responseRateChange","responseTimeTrend","responseTimeChange","ratingDistribution","recentReviews","locationPerformance","insights","hasRecentReviews","hasAttachments","attachments","dashboardUrl","manageReviewsUrl","nextReportDate"]}
{"timestamp":"2025-08-12T06:08:12.866Z","level":"INFO","message":"Email content generated successfully","environment":"DEVELOPMENT","reportType":"Google Review Reports","htmlSize":22885}
{"timestamp":"2025-08-12T06:08:12.868Z","level":"INFO","message":"Uploading file to S3","environment":"DEVELOPMENT","s3Key":"report-attachments/2025-08-12/5/google_review_report_1754978886910.xlsx","mimeType":"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","bucketName":"gmb-social-assets"}
{"timestamp":"2025-08-12T06:08:15.695Z","level":"INFO","message":"File uploaded successfully to S3","environment":"DEVELOPMENT","s3Key":"report-attachments/2025-08-12/5/google_review_report_1754978886910.xlsx","location":"https://gmb-social-assets.s3.amazonaws.com/report-attachments/2025-08-12/5/google_review_report_1754978886910.xlsx","etag":"\"da59dcd81e7a8bdc385ad7b31561b260\""}
{"timestamp":"2025-08-12T06:08:15.696Z","level":"INFO","message":"Report attachment stored successfully","environment":"DEVELOPMENT","executionLogId":5,"fileName":"google_review_report_1754978886910.xlsx","s3Key":"report-attachments/2025-08-12/5/google_review_report_1754978886910.xlsx","size":9137}
{"timestamp":"2025-08-12T06:08:15.727Z","level":"INFO","message":"Execution log updated successfully","environment":"DEVELOPMENT","executionLogId":5,"affectedRows":1}
{"timestamp":"2025-08-12T06:08:15.728Z","level":"INFO","message":"Report attachments processing completed","environment":"DEVELOPMENT","executionLogId":5,"totalAttachments":1,"successfulUploads":1}
{"timestamp":"2025-08-12T06:08:15.729Z","level":"INFO","message":"Sending report emails","environment":"DEVELOPMENT","toRecipients":1,"ccRecipients":0,"bccRecipients":0,"attachments":1}
{"timestamp":"2025-08-12T06:08:15.730Z","level":"INFO","message":"Downloading attachment from S3","environment":"DEVELOPMENT","fileName":"google_review_report_1754978886910.xlsx","url":"https://gmb-social-assets.s3.amazonaws.com/report-attachments/2025-08-12/5/google_review_report_1754978886910.xlsx"}
{"timestamp":"2025-08-12T06:08:15.731Z","level":"INFO","message":"Parsing S3 URL","environment":"DEVELOPMENT","url":"https://gmb-social-assets.s3.amazonaws.com/report-attachments/2025-08-12/5/google_review_report_1754978886910.xlsx"}
{"timestamp":"2025-08-12T06:08:15.732Z","level":"INFO","message":"Downloading file from S3","environment":"DEVELOPMENT","bucket":"gmb-social-assets","key":"report-attachments/2025-08-12/5/google_review_report_1754978886910.xlsx","url":"https://gmb-social-assets.s3.amazonaws.com/report-attachments/2025-08-12/5/google_review_report_1754978886910.xlsx"}
{"timestamp":"2025-08-12T06:08:18.749Z","level":"INFO","message":"Successfully downloaded file from S3","environment":"DEVELOPMENT","bucket":"gmb-social-assets","key":"report-attachments/2025-08-12/5/google_review_report_1754978886910.xlsx","size":9137,"contentType":"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}
{"timestamp":"2025-08-12T06:08:18.750Z","level":"INFO","message":"Successfully prepared attachment","environment":"DEVELOPMENT","fileName":"google_review_report_1754978886910.xlsx","size":9137}
{"timestamp":"2025-08-12T06:08:18.752Z","level":"INFO","message":"Prepared email attachments","environment":"DEVELOPMENT","totalAttachments":1,"attachmentSizes":[{"name":"google_review_report_1754978886910.xlsx","size":9137,"type":"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}]}
{"timestamp":"2025-08-12T06:08:18.753Z","level":"INFO","message":"Preparing to send email","environment":"DEVELOPMENT","to":1,"cc":0,"bcc":0,"subject":"Google Review Reports - My Report - HealthOnUs - Corporate Office Beside pillar No. 1725, Phase 2, K","attachments":1}
{"timestamp":"2025-08-12T06:08:19.143Z","level":"INFO","message":"Email sent successfully","environment":"DEVELOPMENT","messageIds":["010901989ce4d477-aaf76718-9122-4f18-9099-0841701afca1-000000"],"to":1,"cc":0,"bcc":0,"attachments":1}
{"timestamp":"2025-08-12T06:08:19.185Z","level":"INFO","message":"Email transaction logged successfully","environment":"DEVELOPMENT","transactionLogId":5,"executionLogId":5,"recipientEmail":"<EMAIL>","emailStatus":"sent"}
{"timestamp":"2025-08-12T06:08:19.186Z","level":"INFO","message":"Email transactions batch logged successfully","environment":"DEVELOPMENT","executionLogId":5,"transactionCount":1,"transactionIds":[5]}
{"timestamp":"2025-08-12T06:08:19.188Z","level":"INFO","message":"Report emails sent successfully","environment":"DEVELOPMENT","emailsSent":1,"messageIds":["010901989ce4d477-aaf76718-9122-4f18-9099-0841701afca1-000000"]}
{"timestamp":"2025-08-12T06:08:19.230Z","level":"INFO","message":"Execution log updated successfully","environment":"DEVELOPMENT","executionLogId":5,"affectedRows":1}
{"timestamp":"2025-08-12T06:08:19.231Z","level":"INFO","message":"Report execution completed successfully","environment":"DEVELOPMENT","reportId":1,"executionLogId":5,"emailsSent":1,"executionDuration":12594,"attachmentCount":1}
{"timestamp":"2025-08-12T06:08:19.232Z","level":"INFO","message":"Manual report execution completed","environment":"DEVELOPMENT","requestId":"53a5c588-256e-4989-91f3-8f53b564f164","userId":111,"reportId":1,"executionId":5,"emailsSent":1}
{"timestamp":"2025-08-12T06:08:19.281Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"b649c783-6a22-4a6f-b8f2-73e3220338f9","controller":"reportScheduler","action":"getScheduledReports"}
{"timestamp":"2025-08-12T06:08:19.356Z","level":"INFO","message":"Scheduled reports retrieved successfully","environment":"DEVELOPMENT","requestId":"b649c783-6a22-4a6f-b8f2-73e3220338f9","userId":111,"count":1}
{"timestamp":"2025-08-12T06:11:57.679Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"6ca07b6e-0129-4434-a514-820632fc1a46","controller":"reportScheduler","action":"getSchedulerStats"}
{"timestamp":"2025-08-12T06:11:57.693Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"1eb3930d-1888-443b-a5e6-8427a3c193a0","controller":"reportScheduler","action":"getScheduledReports"}
{"timestamp":"2025-08-12T06:11:58.020Z","level":"INFO","message":"Scheduled reports retrieved successfully","environment":"DEVELOPMENT","requestId":"1eb3930d-1888-443b-a5e6-8427a3c193a0","userId":111,"count":1}
{"timestamp":"2025-08-12T06:11:58.193Z","level":"INFO","message":"Retrieved SES sending statistics","environment":"DEVELOPMENT","dataPoints":17}
{"timestamp":"2025-08-12T07:12:16.998Z","level":"INFO","message":"Instagram Service Configuration","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"hasRedirectUri":true,"clientId":"12424462...","redirectUri":"http://localhost:3000/v1/instagram/callback","apiVersion":"v20.0"}
{"timestamp":"2025-08-12T07:12:17.061Z","level":"INFO","message":"Twitter service initialized","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"redirectUri":"http://***********:5173/business-management/twitter/callback","tokenURL":"https://api.x.com/2/oauth2/token","authURL":"https://twitter.com/i/oauth2/authorize"}
{"timestamp":"2025-08-12T07:12:20.635Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-08-12T01:42:20.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-08-12T07:12:38.527Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"c8ed2830-5b52-49a1-9d14-884839496726","controller":"reportScheduler","action":"runReportNow"}
{"timestamp":"2025-08-12T07:12:38.576Z","level":"INFO","message":"Starting report execution","environment":"DEVELOPMENT","reportId":1,"isManualTrigger":true}
{"timestamp":"2025-08-12T07:12:38.682Z","level":"INFO","message":"Execution log created successfully","environment":"DEVELOPMENT","executionLogId":6,"scheduledReportId":1}
{"timestamp":"2025-08-12T07:12:38.685Z","level":"INFO","message":"Execution log created","environment":"DEVELOPMENT","reportId":1,"executionLogId":6,"reportName":"My Report - HealthOnUs - Corporate Office Beside pillar No. 1725, Phase 2, Kavuri Hills, Madhapur"}
{"timestamp":"2025-08-12T07:12:38.688Z","level":"INFO","message":"Generating report data","environment":"DEVELOPMENT","reportId":1,"reportType":"Google Review Reports","reportName":"My Report - HealthOnUs - Corporate Office Beside pillar No. 1725, Phase 2, Kavuri Hills, Madhapur"}
{"timestamp":"2025-08-12T07:12:38.794Z","level":"INFO","message":"Fetching real data for report","environment":"DEVELOPMENT","reportType":"Google Review Reports","userId":111,"filters":{},"dateRange":{"fromDate":"2025-07-13","toDate":"2025-08-12"}}
{"timestamp":"2025-08-12T07:12:38.915Z","level":"ERROR","message":"Error fetching Google Review data:","environment":"DEVELOPMENT","stack":"TypeError: ReportsModel.generateReviewsReport is not a function\n    at ReportExecutionService.fetchGoogleReviewData (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\reportExecutionService.js:640:43)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async ReportExecutionService.fetchRealDataByType (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\reportExecutionService.js:267:18)\n    at async ReportExecutionService.generateReportData (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\reportExecutionService.js:171:27)\n    at async ReportExecutionService.executeReport (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\reportExecutionService.js:56:26)\n    at async runReportNow (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\reportScheduler.controller.js:530:20)"}
{"timestamp":"2025-08-12T07:12:38.918Z","level":"INFO","message":"Report data generated successfully","environment":"DEVELOPMENT","reportId":1,"dataKeys":["reportName","reportType","dateRange","dateRangeObject","filters","generatedAt","recipientName","totalReviews","averageRating","newReviews","responseRate","averageResponseTime","ratingDistribution","recentReviews","locationPerformance","insights","dashboardUrl","nextReportDate"],"dataSize":673}
{"timestamp":"2025-08-12T07:12:38.924Z","level":"INFO","message":"Generating report attachments","environment":"DEVELOPMENT","reportType":"Google Review Reports"}
{"timestamp":"2025-08-12T07:12:39.035Z","level":"INFO","message":"Google Review Excel report generated successfully","environment":"DEVELOPMENT","fileName":"google_review_report_1754982758936.xlsx","sheets":4}
{"timestamp":"2025-08-12T07:12:39.041Z","level":"INFO","message":"Uploading file to S3","environment":"DEVELOPMENT","s3Key":"reports/2025/8/google_review_report_1754982758936.xlsx","mimeType":"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","bucketName":"gmb-social-assets"}
{"timestamp":"2025-08-12T07:12:41.988Z","level":"INFO","message":"File uploaded successfully to S3","environment":"DEVELOPMENT","s3Key":"reports/2025/8/google_review_report_1754982758936.xlsx","location":"https://gmb-social-assets.s3.amazonaws.com/reports/2025/8/google_review_report_1754982758936.xlsx","etag":"\"bf0bdc15940049e4d4fccded93884d90\""}
{"timestamp":"2025-08-12T07:12:41.995Z","level":"INFO","message":"Report uploaded to S3 successfully","environment":"DEVELOPMENT","fileName":"google_review_report_1754982758936.xlsx","s3Key":"reports/2025/8/google_review_report_1754982758936.xlsx","url":"https://gmb-social-assets.s3.amazonaws.com/reports/2025/8/google_review_report_1754982758936.xlsx"}
{"timestamp":"2025-08-12T07:12:41.998Z","level":"INFO","message":"Complete report package generated successfully","environment":"DEVELOPMENT","reportType":"Google Review Reports","hasExcel":true,"hasPDF":false,"attachments":1}
{"timestamp":"2025-08-12T07:12:41.999Z","level":"INFO","message":"Parsing S3 URL","environment":"DEVELOPMENT","url":"https://gmb-social-assets.s3.amazonaws.com/reports/2025/8/google_review_report_1754982758936.xlsx"}
{"timestamp":"2025-08-12T07:12:42.000Z","level":"INFO","message":"Downloading file from S3","environment":"DEVELOPMENT","bucket":"gmb-social-assets","key":"reports/2025/8/google_review_report_1754982758936.xlsx","url":"https://gmb-social-assets.s3.amazonaws.com/reports/2025/8/google_review_report_1754982758936.xlsx"}
{"timestamp":"2025-08-12T07:12:44.744Z","level":"INFO","message":"Successfully downloaded file from S3","environment":"DEVELOPMENT","bucket":"gmb-social-assets","key":"reports/2025/8/google_review_report_1754982758936.xlsx","size":9137,"contentType":"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}
{"timestamp":"2025-08-12T07:12:44.745Z","level":"INFO","message":"Report attachments generated successfully","environment":"DEVELOPMENT","reportType":"Google Review Reports","attachmentCount":1}
{"timestamp":"2025-08-12T07:12:44.747Z","level":"INFO","message":"Generating email content","environment":"DEVELOPMENT","reportType":"Google Review Reports"}
{"timestamp":"2025-08-12T07:12:44.754Z","level":"INFO","message":"Base email template loaded successfully","environment":"DEVELOPMENT"}
{"timestamp":"2025-08-12T07:12:44.756Z","level":"INFO","message":"Handlebars helpers registered successfully","environment":"DEVELOPMENT"}
{"timestamp":"2025-08-12T07:12:44.758Z","level":"INFO","message":"Email template service initialized successfully","environment":"DEVELOPMENT"}
{"timestamp":"2025-08-12T07:12:44.765Z","level":"INFO","message":"Template 'googleReviewReports' loaded and compiled successfully","environment":"DEVELOPMENT"}
{"timestamp":"2025-08-12T07:12:44.803Z","level":"INFO","message":"Template 'googleReviewReports' rendered successfully","environment":"DEVELOPMENT","dataKeys":["subject","recipientName","reportName","dateRange","generatedDate","locationCount","totalReviews","averageRating","newReviews","responseRate","averageResponseTime","ratingTrend","ratingChange","newReviewsTrend","newReviewsChange","responseRateTrend","responseRateChange","responseTimeTrend","responseTimeChange","ratingDistribution","recentReviews","locationPerformance","insights","hasRecentReviews","hasAttachments","attachments","dashboardUrl","manageReviewsUrl","nextReportDate"]}
{"timestamp":"2025-08-12T07:12:44.804Z","level":"INFO","message":"Email content generated successfully","environment":"DEVELOPMENT","reportType":"Google Review Reports","htmlSize":16500}
{"timestamp":"2025-08-12T07:12:44.806Z","level":"INFO","message":"Uploading file to S3","environment":"DEVELOPMENT","s3Key":"report-attachments/2025-08-12/6/google_review_report_1754982758936.xlsx","mimeType":"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","bucketName":"gmb-social-assets"}
{"timestamp":"2025-08-12T07:12:47.638Z","level":"INFO","message":"File uploaded successfully to S3","environment":"DEVELOPMENT","s3Key":"report-attachments/2025-08-12/6/google_review_report_1754982758936.xlsx","location":"https://gmb-social-assets.s3.amazonaws.com/report-attachments/2025-08-12/6/google_review_report_1754982758936.xlsx","etag":"\"bf0bdc15940049e4d4fccded93884d90\""}
{"timestamp":"2025-08-12T07:12:47.639Z","level":"INFO","message":"Report attachment stored successfully","environment":"DEVELOPMENT","executionLogId":6,"fileName":"google_review_report_1754982758936.xlsx","s3Key":"report-attachments/2025-08-12/6/google_review_report_1754982758936.xlsx","size":9137}
{"timestamp":"2025-08-12T07:12:47.690Z","level":"INFO","message":"Execution log updated successfully","environment":"DEVELOPMENT","executionLogId":6,"affectedRows":1}
{"timestamp":"2025-08-12T07:12:47.691Z","level":"INFO","message":"Report attachments processing completed","environment":"DEVELOPMENT","executionLogId":6,"totalAttachments":1,"successfulUploads":1}
{"timestamp":"2025-08-12T07:12:47.693Z","level":"INFO","message":"Sending report emails","environment":"DEVELOPMENT","toRecipients":1,"ccRecipients":0,"bccRecipients":0,"attachments":1}
{"timestamp":"2025-08-12T07:12:47.695Z","level":"INFO","message":"Downloading attachment from S3","environment":"DEVELOPMENT","fileName":"google_review_report_1754982758936.xlsx","url":"https://gmb-social-assets.s3.amazonaws.com/report-attachments/2025-08-12/6/google_review_report_1754982758936.xlsx"}
{"timestamp":"2025-08-12T07:12:47.698Z","level":"INFO","message":"Parsing S3 URL","environment":"DEVELOPMENT","url":"https://gmb-social-assets.s3.amazonaws.com/report-attachments/2025-08-12/6/google_review_report_1754982758936.xlsx"}
{"timestamp":"2025-08-12T07:12:47.699Z","level":"INFO","message":"Downloading file from S3","environment":"DEVELOPMENT","bucket":"gmb-social-assets","key":"report-attachments/2025-08-12/6/google_review_report_1754982758936.xlsx","url":"https://gmb-social-assets.s3.amazonaws.com/report-attachments/2025-08-12/6/google_review_report_1754982758936.xlsx"}
{"timestamp":"2025-08-12T07:12:50.510Z","level":"INFO","message":"Successfully downloaded file from S3","environment":"DEVELOPMENT","bucket":"gmb-social-assets","key":"report-attachments/2025-08-12/6/google_review_report_1754982758936.xlsx","size":9137,"contentType":"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}
{"timestamp":"2025-08-12T07:12:50.512Z","level":"INFO","message":"Successfully prepared attachment","environment":"DEVELOPMENT","fileName":"google_review_report_1754982758936.xlsx","size":9137}
{"timestamp":"2025-08-12T07:12:50.513Z","level":"INFO","message":"Prepared email attachments","environment":"DEVELOPMENT","totalAttachments":1,"attachmentSizes":[{"name":"google_review_report_1754982758936.xlsx","size":9137,"type":"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}]}
{"timestamp":"2025-08-12T07:12:50.516Z","level":"INFO","message":"Preparing to send email","environment":"DEVELOPMENT","to":1,"cc":0,"bcc":0,"subject":"Google Review Reports - My Report - HealthOnUs - Corporate Office Beside pillar No. 1725, Phase 2, K","attachments":1}
{"timestamp":"2025-08-12T07:12:50.964Z","level":"INFO","message":"Email sent successfully","environment":"DEVELOPMENT","messageIds":["010901989d1fe8ce-51470168-2db1-49e9-a4a0-9a1de6e59020-000000"],"to":1,"cc":0,"bcc":0,"attachments":1}
{"timestamp":"2025-08-12T07:12:51.006Z","level":"INFO","message":"Email transaction logged successfully","environment":"DEVELOPMENT","transactionLogId":6,"executionLogId":6,"recipientEmail":"<EMAIL>","emailStatus":"sent"}
{"timestamp":"2025-08-12T07:12:51.007Z","level":"INFO","message":"Email transactions batch logged successfully","environment":"DEVELOPMENT","executionLogId":6,"transactionCount":1,"transactionIds":[6]}
{"timestamp":"2025-08-12T07:12:51.008Z","level":"INFO","message":"Report emails sent successfully","environment":"DEVELOPMENT","emailsSent":1,"messageIds":["010901989d1fe8ce-51470168-2db1-49e9-a4a0-9a1de6e59020-000000"]}
{"timestamp":"2025-08-12T07:12:51.051Z","level":"INFO","message":"Execution log updated successfully","environment":"DEVELOPMENT","executionLogId":6,"affectedRows":1}
{"timestamp":"2025-08-12T07:12:51.052Z","level":"INFO","message":"Report execution completed successfully","environment":"DEVELOPMENT","reportId":1,"executionLogId":6,"emailsSent":1,"executionDuration":12433,"attachmentCount":1}
{"timestamp":"2025-08-12T07:12:51.053Z","level":"INFO","message":"Manual report execution completed","environment":"DEVELOPMENT","requestId":"c8ed2830-5b52-49a1-9d14-884839496726","userId":111,"reportId":1,"executionId":6,"emailsSent":1}
{"timestamp":"2025-08-12T07:12:51.145Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"41f6cf16-b319-4420-b8aa-1657b9d4c276","controller":"reportScheduler","action":"getScheduledReports"}
{"timestamp":"2025-08-12T07:12:51.227Z","level":"INFO","message":"Scheduled reports retrieved successfully","environment":"DEVELOPMENT","requestId":"41f6cf16-b319-4420-b8aa-1657b9d4c276","userId":111,"count":1}
{"timestamp":"2025-08-12T07:33:19.341Z","level":"INFO","message":"Instagram Service Configuration","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"hasRedirectUri":true,"clientId":"12424462...","redirectUri":"http://localhost:3000/v1/instagram/callback","apiVersion":"v20.0"}
{"timestamp":"2025-08-12T07:33:19.394Z","level":"INFO","message":"Twitter service initialized","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"redirectUri":"http://***********:5173/business-management/twitter/callback","tokenURL":"https://api.x.com/2/oauth2/token","authURL":"https://twitter.com/i/oauth2/authorize"}
{"timestamp":"2025-08-12T07:33:28.003Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-08-12T02:03:28.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-08-12T07:36:05.847Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"56cce8a4-06de-4013-9572-573025da7942","controller":"reportScheduler","action":"getSchedulerStats"}
{"timestamp":"2025-08-12T07:36:05.877Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"6b58cd3c-42eb-4dca-848c-4166f379e9fa","controller":"reportScheduler","action":"getScheduledReports"}
{"timestamp":"2025-08-12T07:36:06.181Z","level":"INFO","message":"Scheduled reports retrieved successfully","environment":"DEVELOPMENT","requestId":"6b58cd3c-42eb-4dca-848c-4166f379e9fa","userId":111,"count":1}
{"timestamp":"2025-08-12T07:36:06.768Z","level":"INFO","message":"Retrieved SES sending statistics","environment":"DEVELOPMENT","dataPoints":24}
{"timestamp":"2025-08-12T07:36:29.561Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"a81d86f5-e964-4f2f-8961-f51bb54958fd","controller":"reportScheduler","action":"getScheduledReports"}
{"timestamp":"2025-08-12T07:36:29.619Z","level":"INFO","message":"Scheduled reports retrieved successfully","environment":"DEVELOPMENT","requestId":"a81d86f5-e964-4f2f-8961-f51bb54958fd","userId":111,"count":1}
{"timestamp":"2025-08-12T07:37:10.275Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"b0754f82-fdff-483d-b534-cc5363fbac1b","controller":"reportScheduler","action":"getScheduledReports"}
{"timestamp":"2025-08-12T07:37:10.333Z","level":"INFO","message":"Scheduled reports retrieved successfully","environment":"DEVELOPMENT","requestId":"b0754f82-fdff-483d-b534-cc5363fbac1b","userId":111,"count":1}
{"timestamp":"2025-08-12T07:37:48.242Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"c9936939-be99-426d-8dd8-ac20e5ef1706","controller":"reportScheduler","action":"getScheduledReports"}
{"timestamp":"2025-08-12T07:37:48.307Z","level":"INFO","message":"Scheduled reports retrieved successfully","environment":"DEVELOPMENT","requestId":"c9936939-be99-426d-8dd8-ac20e5ef1706","userId":111,"count":1}
{"timestamp":"2025-08-12T07:40:24.151Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"e82f1542-1cd6-415f-bff4-7de3739fb3a4","controller":"reportScheduler","action":"getSchedulerStats"}
{"timestamp":"2025-08-12T07:40:24.168Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"a63fb57a-b8e2-4bdc-902e-bcb85e361dcb","controller":"reportScheduler","action":"getScheduledReports"}
{"timestamp":"2025-08-12T07:40:24.238Z","level":"INFO","message":"Scheduled reports retrieved successfully","environment":"DEVELOPMENT","requestId":"a63fb57a-b8e2-4bdc-902e-bcb85e361dcb","userId":111,"count":1}
{"timestamp":"2025-08-12T07:40:24.451Z","level":"INFO","message":"Retrieved SES sending statistics","environment":"DEVELOPMENT","dataPoints":21}
{"timestamp":"2025-08-12T07:40:29.365Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"9959c4c0-ca09-4636-9412-6ba6f412f0eb","controller":"reportScheduler","action":"runReportNow"}
{"timestamp":"2025-08-12T07:40:29.397Z","level":"INFO","message":"Starting report execution","environment":"DEVELOPMENT","reportId":1,"isManualTrigger":true}
{"timestamp":"2025-08-12T07:40:29.458Z","level":"INFO","message":"Execution log created successfully","environment":"DEVELOPMENT","executionLogId":7,"scheduledReportId":1}
{"timestamp":"2025-08-12T07:40:29.460Z","level":"INFO","message":"Execution log created","environment":"DEVELOPMENT","reportId":1,"executionLogId":7,"reportName":"My Report - HealthOnUs - Corporate Office Beside pillar No. 1725, Phase 2, Kavuri Hills, Madhapur"}
{"timestamp":"2025-08-12T07:40:29.463Z","level":"INFO","message":"Generating report data","environment":"DEVELOPMENT","reportId":1,"reportType":"Google Review Reports","reportName":"My Report - HealthOnUs - Corporate Office Beside pillar No. 1725, Phase 2, Kavuri Hills, Madhapur"}
{"timestamp":"2025-08-12T07:40:29.588Z","level":"INFO","message":"Fetching real data for report","environment":"DEVELOPMENT","reportType":"Google Review Reports","userId":111,"filters":{},"dateRange":{"fromDate":"2025-07-13","toDate":"2025-08-12"}}
{"timestamp":"2025-08-12T07:40:29.680Z","level":"ERROR","message":"Error fetching Google Review data:","environment":"DEVELOPMENT","stack":"TypeError: ReportsModel.generateReviewsReport is not a function\n    at ReportExecutionService.fetchGoogleReviewData (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\reportExecutionService.js:640:43)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async ReportExecutionService.fetchRealDataByType (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\reportExecutionService.js:267:18)\n    at async ReportExecutionService.generateReportData (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\reportExecutionService.js:171:27)\n    at async ReportExecutionService.executeReport (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\reportExecutionService.js:56:26)\n    at async runReportNow (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\reportScheduler.controller.js:530:20)"}
{"timestamp":"2025-08-12T07:40:29.685Z","level":"INFO","message":"Report data generated successfully","environment":"DEVELOPMENT","reportId":1,"dataKeys":["reportName","reportType","dateRange","dateRangeObject","filters","generatedAt","recipientName","totalReviews","averageRating","newReviews","responseRate","averageResponseTime","ratingDistribution","recentReviews","locationPerformance","insights","dashboardUrl","nextReportDate"],"dataSize":673}
{"timestamp":"2025-08-12T07:40:29.692Z","level":"INFO","message":"Generating report attachments","environment":"DEVELOPMENT","reportType":"Google Review Reports"}
{"timestamp":"2025-08-12T07:40:29.879Z","level":"INFO","message":"Google Review Excel report generated successfully","environment":"DEVELOPMENT","fileName":"google_review_report_1754984429715.xlsx","sheets":4}
{"timestamp":"2025-08-12T07:40:29.889Z","level":"INFO","message":"Uploading file to S3","environment":"DEVELOPMENT","s3Key":"reports/2025/8/google_review_report_1754984429715.xlsx","mimeType":"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","bucketName":"gmb-social-assets"}
{"timestamp":"2025-08-12T07:40:32.858Z","level":"INFO","message":"File uploaded successfully to S3","environment":"DEVELOPMENT","s3Key":"reports/2025/8/google_review_report_1754984429715.xlsx","location":"https://gmb-social-assets.s3.amazonaws.com/reports/2025/8/google_review_report_1754984429715.xlsx","etag":"\"c962a77b760348f9be02cda176762155\""}
{"timestamp":"2025-08-12T07:40:32.868Z","level":"INFO","message":"Report uploaded to S3 successfully","environment":"DEVELOPMENT","fileName":"google_review_report_1754984429715.xlsx","s3Key":"reports/2025/8/google_review_report_1754984429715.xlsx","url":"https://gmb-social-assets.s3.amazonaws.com/reports/2025/8/google_review_report_1754984429715.xlsx"}
{"timestamp":"2025-08-12T07:40:32.884Z","level":"INFO","message":"Complete report package generated successfully","environment":"DEVELOPMENT","reportType":"Google Review Reports","hasExcel":true,"hasPDF":false,"attachments":1}
{"timestamp":"2025-08-12T07:40:32.893Z","level":"INFO","message":"Parsing S3 URL","environment":"DEVELOPMENT","url":"https://gmb-social-assets.s3.amazonaws.com/reports/2025/8/google_review_report_1754984429715.xlsx"}
{"timestamp":"2025-08-12T07:40:32.895Z","level":"INFO","message":"Downloading file from S3","environment":"DEVELOPMENT","bucket":"gmb-social-assets","key":"reports/2025/8/google_review_report_1754984429715.xlsx","url":"https://gmb-social-assets.s3.amazonaws.com/reports/2025/8/google_review_report_1754984429715.xlsx"}
{"timestamp":"2025-08-12T07:40:36.754Z","level":"INFO","message":"Successfully downloaded file from S3","environment":"DEVELOPMENT","bucket":"gmb-social-assets","key":"reports/2025/8/google_review_report_1754984429715.xlsx","size":9138,"contentType":"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}
{"timestamp":"2025-08-12T07:40:36.756Z","level":"INFO","message":"Report attachments generated successfully","environment":"DEVELOPMENT","reportType":"Google Review Reports","attachmentCount":1}
{"timestamp":"2025-08-12T07:40:36.757Z","level":"INFO","message":"Generating email content","environment":"DEVELOPMENT","reportType":"Google Review Reports"}
{"timestamp":"2025-08-12T07:40:36.816Z","level":"INFO","message":"Base email template loaded successfully","environment":"DEVELOPMENT"}
{"timestamp":"2025-08-12T07:40:36.818Z","level":"INFO","message":"Handlebars helpers registered successfully","environment":"DEVELOPMENT"}
{"timestamp":"2025-08-12T07:40:36.819Z","level":"INFO","message":"Email template service initialized successfully","environment":"DEVELOPMENT"}
{"timestamp":"2025-08-12T07:40:36.834Z","level":"INFO","message":"Template 'googleReviewReports' loaded and compiled successfully","environment":"DEVELOPMENT"}
{"timestamp":"2025-08-12T07:40:36.962Z","level":"INFO","message":"Template 'googleReviewReports' rendered successfully","environment":"DEVELOPMENT","dataKeys":["subject","recipientName","reportName","dateRange","generatedDate","locationCount","totalReviews","averageRating","newReviews","responseRate","averageResponseTime","ratingTrend","ratingChange","newReviewsTrend","newReviewsChange","responseRateTrend","responseRateChange","responseTimeTrend","responseTimeChange","ratingDistribution","recentReviews","locationPerformance","insights","hasRecentReviews","hasAttachments","attachments","dashboardUrl","manageReviewsUrl","nextReportDate"]}
{"timestamp":"2025-08-12T07:40:36.972Z","level":"INFO","message":"Email content generated successfully","environment":"DEVELOPMENT","reportType":"Google Review Reports","htmlSize":15951}
{"timestamp":"2025-08-12T07:40:36.976Z","level":"INFO","message":"Uploading file to S3","environment":"DEVELOPMENT","s3Key":"report-attachments/2025-08-12/7/google_review_report_1754984429715.xlsx","mimeType":"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","bucketName":"gmb-social-assets"}
{"timestamp":"2025-08-12T07:40:39.738Z","level":"INFO","message":"File uploaded successfully to S3","environment":"DEVELOPMENT","s3Key":"report-attachments/2025-08-12/7/google_review_report_1754984429715.xlsx","location":"https://gmb-social-assets.s3.amazonaws.com/report-attachments/2025-08-12/7/google_review_report_1754984429715.xlsx","etag":"\"c962a77b760348f9be02cda176762155\""}
{"timestamp":"2025-08-12T07:40:39.739Z","level":"INFO","message":"Report attachment stored successfully","environment":"DEVELOPMENT","executionLogId":7,"fileName":"google_review_report_1754984429715.xlsx","s3Key":"report-attachments/2025-08-12/7/google_review_report_1754984429715.xlsx","size":9138}
{"timestamp":"2025-08-12T07:40:39.771Z","level":"INFO","message":"Execution log updated successfully","environment":"DEVELOPMENT","executionLogId":7,"affectedRows":1}
{"timestamp":"2025-08-12T07:40:39.772Z","level":"INFO","message":"Report attachments processing completed","environment":"DEVELOPMENT","executionLogId":7,"totalAttachments":1,"successfulUploads":1}
{"timestamp":"2025-08-12T07:40:39.774Z","level":"INFO","message":"Sending report emails","environment":"DEVELOPMENT","toRecipients":1,"ccRecipients":0,"bccRecipients":0,"attachments":1}
{"timestamp":"2025-08-12T07:40:39.775Z","level":"INFO","message":"Downloading attachment from S3","environment":"DEVELOPMENT","fileName":"google_review_report_1754984429715.xlsx","url":"https://gmb-social-assets.s3.amazonaws.com/report-attachments/2025-08-12/7/google_review_report_1754984429715.xlsx"}
{"timestamp":"2025-08-12T07:40:39.777Z","level":"INFO","message":"Parsing S3 URL","environment":"DEVELOPMENT","url":"https://gmb-social-assets.s3.amazonaws.com/report-attachments/2025-08-12/7/google_review_report_1754984429715.xlsx"}
{"timestamp":"2025-08-12T07:40:39.778Z","level":"INFO","message":"Downloading file from S3","environment":"DEVELOPMENT","bucket":"gmb-social-assets","key":"report-attachments/2025-08-12/7/google_review_report_1754984429715.xlsx","url":"https://gmb-social-assets.s3.amazonaws.com/report-attachments/2025-08-12/7/google_review_report_1754984429715.xlsx"}
{"timestamp":"2025-08-12T07:40:42.530Z","level":"INFO","message":"Successfully downloaded file from S3","environment":"DEVELOPMENT","bucket":"gmb-social-assets","key":"report-attachments/2025-08-12/7/google_review_report_1754984429715.xlsx","size":9138,"contentType":"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}
{"timestamp":"2025-08-12T07:40:42.531Z","level":"INFO","message":"Successfully prepared attachment","environment":"DEVELOPMENT","fileName":"google_review_report_1754984429715.xlsx","size":9138}
{"timestamp":"2025-08-12T07:40:42.532Z","level":"INFO","message":"Prepared email attachments","environment":"DEVELOPMENT","totalAttachments":1,"attachmentSizes":[{"name":"google_review_report_1754984429715.xlsx","size":9138,"type":"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}]}
{"timestamp":"2025-08-12T07:40:42.533Z","level":"INFO","message":"Preparing to send email","environment":"DEVELOPMENT","to":1,"cc":0,"bcc":0,"subject":"Google Review Reports - My Report - HealthOnUs - Corporate Office Beside pillar No. 1725, Phase 2, K","attachments":1}
{"timestamp":"2025-08-12T07:40:42.774Z","level":"INFO","message":"Email sent successfully","environment":"DEVELOPMENT","messageIds":["010901989d396b8d-5907d017-c05d-4b5e-817a-08de634df738-000000"],"to":1,"cc":0,"bcc":0,"attachments":1}
{"timestamp":"2025-08-12T07:40:42.804Z","level":"INFO","message":"Email transaction logged successfully","environment":"DEVELOPMENT","transactionLogId":7,"executionLogId":7,"recipientEmail":"<EMAIL>","emailStatus":"sent"}
{"timestamp":"2025-08-12T07:40:42.806Z","level":"INFO","message":"Email transactions batch logged successfully","environment":"DEVELOPMENT","executionLogId":7,"transactionCount":1,"transactionIds":[7]}
{"timestamp":"2025-08-12T07:40:42.807Z","level":"INFO","message":"Report emails sent successfully","environment":"DEVELOPMENT","emailsSent":1,"messageIds":["010901989d396b8d-5907d017-c05d-4b5e-817a-08de634df738-000000"]}
{"timestamp":"2025-08-12T07:40:42.835Z","level":"INFO","message":"Execution log updated successfully","environment":"DEVELOPMENT","executionLogId":7,"affectedRows":1}
{"timestamp":"2025-08-12T07:40:42.838Z","level":"INFO","message":"Report execution completed successfully","environment":"DEVELOPMENT","reportId":1,"executionLogId":7,"emailsSent":1,"executionDuration":13411,"attachmentCount":1}
{"timestamp":"2025-08-12T07:40:42.839Z","level":"INFO","message":"Manual report execution completed","environment":"DEVELOPMENT","requestId":"9959c4c0-ca09-4636-9412-6ba6f412f0eb","userId":111,"reportId":1,"executionId":7,"emailsSent":1}
{"timestamp":"2025-08-12T07:40:42.876Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"59297dfe-9e8e-4381-a6fd-58de8a917510","controller":"reportScheduler","action":"getScheduledReports"}
{"timestamp":"2025-08-12T07:40:42.924Z","level":"INFO","message":"Scheduled reports retrieved successfully","environment":"DEVELOPMENT","requestId":"59297dfe-9e8e-4381-a6fd-58de8a917510","userId":111,"count":1}
{"timestamp":"2025-08-12T09:09:01.767Z","level":"INFO","message":"Instagram Service Configuration","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"hasRedirectUri":true,"clientId":"12424462...","redirectUri":"http://localhost:3000/v1/instagram/callback","apiVersion":"v20.0"}
{"timestamp":"2025-08-12T09:09:01.866Z","level":"INFO","message":"Twitter service initialized","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"redirectUri":"http://***********:5173/business-management/twitter/callback","tokenURL":"https://api.x.com/2/oauth2/token","authURL":"https://twitter.com/i/oauth2/authorize"}
{"timestamp":"2025-08-12T09:09:06.169Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-08-12T03:39:06.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-08-12T09:11:25.752Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"d9d2e92a-6004-4b6b-86a3-fb20c81ea7c4","controller":"reportScheduler","action":"getSchedulerStats"}
{"timestamp":"2025-08-12T09:11:25.849Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"e88ab182-46a0-4d4b-8932-dc2707196dae","controller":"reportScheduler","action":"getScheduledReports"}
{"timestamp":"2025-08-12T09:11:26.317Z","level":"INFO","message":"Scheduled reports retrieved successfully","environment":"DEVELOPMENT","requestId":"e88ab182-46a0-4d4b-8932-dc2707196dae","userId":111,"count":1}
{"timestamp":"2025-08-12T09:11:26.490Z","level":"INFO","message":"Retrieved SES sending statistics","environment":"DEVELOPMENT","dataPoints":20}
{"timestamp":"2025-08-12T09:11:27.822Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"f76fda39-830d-4000-9fe4-a066d5f27de8","controller":"reportScheduler","action":"runReportNow"}
{"timestamp":"2025-08-12T09:11:27.885Z","level":"INFO","message":"Starting report execution","environment":"DEVELOPMENT","reportId":1,"isManualTrigger":true}
{"timestamp":"2025-08-12T09:11:27.972Z","level":"INFO","message":"Execution log created successfully","environment":"DEVELOPMENT","executionLogId":9,"scheduledReportId":1}
{"timestamp":"2025-08-12T09:11:27.976Z","level":"INFO","message":"Execution log created","environment":"DEVELOPMENT","reportId":1,"executionLogId":9,"reportName":"My Report - HealthOnUs - Corporate Office Beside pillar No. 1725, Phase 2, Kavuri Hills, Madhapur"}
{"timestamp":"2025-08-12T09:11:27.984Z","level":"INFO","message":"Generating report data","environment":"DEVELOPMENT","reportId":1,"reportType":"Google Review Reports","reportName":"My Report - HealthOnUs - Corporate Office Beside pillar No. 1725, Phase 2, Kavuri Hills, Madhapur"}
{"timestamp":"2025-08-12T09:11:28.231Z","level":"INFO","message":"Fetching real data for report","environment":"DEVELOPMENT","reportType":"Google Review Reports","userId":111,"filters":{},"dateRange":{"fromDate":"2025-07-13","toDate":"2025-08-12"}}
{"timestamp":"2025-08-12T09:11:28.344Z","level":"ERROR","message":"Error fetching Google Review data:","environment":"DEVELOPMENT","stack":"TypeError: ReportsModel.generateReviewsReport is not a function\n    at ReportExecutionService.fetchGoogleReviewData (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\reportExecutionService.js:640:43)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async ReportExecutionService.fetchRealDataByType (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\reportExecutionService.js:267:18)\n    at async ReportExecutionService.generateReportData (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\reportExecutionService.js:171:27)\n    at async ReportExecutionService.executeReport (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\reportExecutionService.js:56:26)\n    at async runReportNow (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\reportScheduler.controller.js:530:20)"}
{"timestamp":"2025-08-12T09:11:28.347Z","level":"INFO","message":"Report data generated successfully","environment":"DEVELOPMENT","reportId":1,"dataKeys":["reportName","reportType","dateRange","dateRangeObject","filters","generatedAt","recipientName","totalReviews","averageRating","newReviews","responseRate","averageResponseTime","ratingDistribution","recentReviews","locationPerformance","insights","dashboardUrl","nextReportDate"],"dataSize":673}
{"timestamp":"2025-08-12T09:11:28.361Z","level":"INFO","message":"Generating report attachments","environment":"DEVELOPMENT","reportType":"Google Review Reports"}
{"timestamp":"2025-08-12T09:11:28.537Z","level":"INFO","message":"Google Review Excel report generated successfully","environment":"DEVELOPMENT","fileName":"google_review_report_1754989888370.xlsx","sheets":4}
{"timestamp":"2025-08-12T09:11:28.551Z","level":"INFO","message":"Uploading file to S3","environment":"DEVELOPMENT","s3Key":"reports/2025/8/google_review_report_1754989888370.xlsx","mimeType":"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","bucketName":"gmb-social-assets"}
{"timestamp":"2025-08-12T09:11:31.488Z","level":"INFO","message":"File uploaded successfully to S3","environment":"DEVELOPMENT","s3Key":"reports/2025/8/google_review_report_1754989888370.xlsx","location":"https://gmb-social-assets.s3.amazonaws.com/reports/2025/8/google_review_report_1754989888370.xlsx","etag":"\"bfc96ea34898adbdec469ae4298fbf9f\""}
{"timestamp":"2025-08-12T09:11:31.491Z","level":"INFO","message":"Report uploaded to S3 successfully","environment":"DEVELOPMENT","fileName":"google_review_report_1754989888370.xlsx","s3Key":"reports/2025/8/google_review_report_1754989888370.xlsx","url":"https://gmb-social-assets.s3.amazonaws.com/reports/2025/8/google_review_report_1754989888370.xlsx"}
{"timestamp":"2025-08-12T09:11:31.493Z","level":"INFO","message":"Complete report package generated successfully","environment":"DEVELOPMENT","reportType":"Google Review Reports","hasExcel":true,"hasPDF":false,"attachments":1}
{"timestamp":"2025-08-12T09:11:31.495Z","level":"INFO","message":"Parsing S3 URL","environment":"DEVELOPMENT","url":"https://gmb-social-assets.s3.amazonaws.com/reports/2025/8/google_review_report_1754989888370.xlsx"}
{"timestamp":"2025-08-12T09:11:31.497Z","level":"INFO","message":"Downloading file from S3","environment":"DEVELOPMENT","bucket":"gmb-social-assets","key":"reports/2025/8/google_review_report_1754989888370.xlsx","url":"https://gmb-social-assets.s3.amazonaws.com/reports/2025/8/google_review_report_1754989888370.xlsx"}
{"timestamp":"2025-08-12T09:11:34.279Z","level":"INFO","message":"Successfully downloaded file from S3","environment":"DEVELOPMENT","bucket":"gmb-social-assets","key":"reports/2025/8/google_review_report_1754989888370.xlsx","size":9137,"contentType":"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}
{"timestamp":"2025-08-12T09:11:34.280Z","level":"INFO","message":"Report attachments generated successfully","environment":"DEVELOPMENT","reportType":"Google Review Reports","attachmentCount":1}
{"timestamp":"2025-08-12T09:11:34.282Z","level":"INFO","message":"Generating email content","environment":"DEVELOPMENT","reportType":"Google Review Reports"}
{"timestamp":"2025-08-12T09:11:34.311Z","level":"INFO","message":"Base email template loaded successfully","environment":"DEVELOPMENT"}
{"timestamp":"2025-08-12T09:11:34.312Z","level":"INFO","message":"Handlebars helpers registered successfully","environment":"DEVELOPMENT"}
{"timestamp":"2025-08-12T09:11:34.313Z","level":"INFO","message":"Email template service initialized successfully","environment":"DEVELOPMENT"}
{"timestamp":"2025-08-12T09:11:34.317Z","level":"INFO","message":"Template 'googleReviewReports' loaded and compiled successfully","environment":"DEVELOPMENT"}
{"timestamp":"2025-08-12T09:11:34.393Z","level":"INFO","message":"Template 'googleReviewReports' rendered successfully","environment":"DEVELOPMENT","dataKeys":["subject","recipientName","reportName","dateRange","generatedDate","locationCount","totalReviews","averageRating","newReviews","responseRate","averageResponseTime","ratingTrend","ratingChange","newReviewsTrend","newReviewsChange","responseRateTrend","responseRateChange","responseTimeTrend","responseTimeChange","ratingDistribution","recentReviews","locationPerformance","insights","hasRecentReviews","hasAttachments","attachments","dashboardUrl","manageReviewsUrl","nextReportDate"]}
{"timestamp":"2025-08-12T09:11:34.396Z","level":"INFO","message":"Email content generated successfully","environment":"DEVELOPMENT","reportType":"Google Review Reports","htmlSize":16481}
{"timestamp":"2025-08-12T09:11:34.398Z","level":"INFO","message":"Uploading file to S3","environment":"DEVELOPMENT","s3Key":"report-attachments/2025-08-12/9/google_review_report_1754989888370.xlsx","mimeType":"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","bucketName":"gmb-social-assets"}
{"timestamp":"2025-08-12T09:11:37.266Z","level":"INFO","message":"File uploaded successfully to S3","environment":"DEVELOPMENT","s3Key":"report-attachments/2025-08-12/9/google_review_report_1754989888370.xlsx","location":"https://gmb-social-assets.s3.amazonaws.com/report-attachments/2025-08-12/9/google_review_report_1754989888370.xlsx","etag":"\"bfc96ea34898adbdec469ae4298fbf9f\""}
{"timestamp":"2025-08-12T09:11:37.268Z","level":"INFO","message":"Report attachment stored successfully","environment":"DEVELOPMENT","executionLogId":9,"fileName":"google_review_report_1754989888370.xlsx","s3Key":"report-attachments/2025-08-12/9/google_review_report_1754989888370.xlsx","size":9137}
{"timestamp":"2025-08-12T09:11:37.315Z","level":"INFO","message":"Execution log updated successfully","environment":"DEVELOPMENT","executionLogId":9,"affectedRows":1}
{"timestamp":"2025-08-12T09:11:37.317Z","level":"INFO","message":"Report attachments processing completed","environment":"DEVELOPMENT","executionLogId":9,"totalAttachments":1,"successfulUploads":1}
{"timestamp":"2025-08-12T09:11:37.319Z","level":"INFO","message":"Sending report emails","environment":"DEVELOPMENT","toRecipients":1,"ccRecipients":0,"bccRecipients":0,"attachments":1}
{"timestamp":"2025-08-12T09:11:37.322Z","level":"INFO","message":"Downloading attachment from S3","environment":"DEVELOPMENT","fileName":"google_review_report_1754989888370.xlsx","url":"https://gmb-social-assets.s3.amazonaws.com/report-attachments/2025-08-12/9/google_review_report_1754989888370.xlsx"}
{"timestamp":"2025-08-12T09:11:37.328Z","level":"INFO","message":"Parsing S3 URL","environment":"DEVELOPMENT","url":"https://gmb-social-assets.s3.amazonaws.com/report-attachments/2025-08-12/9/google_review_report_1754989888370.xlsx"}
{"timestamp":"2025-08-12T09:11:37.331Z","level":"INFO","message":"Downloading file from S3","environment":"DEVELOPMENT","bucket":"gmb-social-assets","key":"report-attachments/2025-08-12/9/google_review_report_1754989888370.xlsx","url":"https://gmb-social-assets.s3.amazonaws.com/report-attachments/2025-08-12/9/google_review_report_1754989888370.xlsx"}
{"timestamp":"2025-08-12T09:11:40.128Z","level":"INFO","message":"Successfully downloaded file from S3","environment":"DEVELOPMENT","bucket":"gmb-social-assets","key":"report-attachments/2025-08-12/9/google_review_report_1754989888370.xlsx","size":9137,"contentType":"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}
{"timestamp":"2025-08-12T09:11:40.130Z","level":"INFO","message":"Successfully prepared attachment","environment":"DEVELOPMENT","fileName":"google_review_report_1754989888370.xlsx","size":9137}
{"timestamp":"2025-08-12T09:11:40.131Z","level":"INFO","message":"Prepared email attachments","environment":"DEVELOPMENT","totalAttachments":1,"attachmentSizes":[{"name":"google_review_report_1754989888370.xlsx","size":9137,"type":"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}]}
{"timestamp":"2025-08-12T09:11:40.133Z","level":"INFO","message":"Preparing to send email","environment":"DEVELOPMENT","to":1,"cc":0,"bcc":0,"subject":"Google Review Reports - My Report - HealthOnUs - Corporate Office Beside pillar No. 1725, Phase 2, K","attachments":1}
{"timestamp":"2025-08-12T09:11:40.409Z","level":"INFO","message":"Email sent successfully","environment":"DEVELOPMENT","messageIds":["010901989d8cb2a7-bf22e388-6aa8-41dd-9f05-065305be530c-000000"],"to":1,"cc":0,"bcc":0,"attachments":1}
{"timestamp":"2025-08-12T09:11:40.443Z","level":"INFO","message":"Email transaction logged successfully","environment":"DEVELOPMENT","transactionLogId":9,"executionLogId":9,"recipientEmail":"<EMAIL>","emailStatus":"sent"}
{"timestamp":"2025-08-12T09:11:40.445Z","level":"INFO","message":"Email transactions batch logged successfully","environment":"DEVELOPMENT","executionLogId":9,"transactionCount":1,"transactionIds":[9]}
{"timestamp":"2025-08-12T09:11:40.446Z","level":"INFO","message":"Report emails sent successfully","environment":"DEVELOPMENT","emailsSent":1,"messageIds":["010901989d8cb2a7-bf22e388-6aa8-41dd-9f05-065305be530c-000000"]}
{"timestamp":"2025-08-12T09:11:40.479Z","level":"INFO","message":"Execution log updated successfully","environment":"DEVELOPMENT","executionLogId":9,"affectedRows":1}
{"timestamp":"2025-08-12T09:11:40.480Z","level":"INFO","message":"Report execution completed successfully","environment":"DEVELOPMENT","reportId":1,"executionLogId":9,"emailsSent":1,"executionDuration":12563,"attachmentCount":1}
{"timestamp":"2025-08-12T09:11:40.482Z","level":"INFO","message":"Manual report execution completed","environment":"DEVELOPMENT","requestId":"f76fda39-830d-4000-9fe4-a066d5f27de8","userId":111,"reportId":1,"executionId":9,"emailsSent":1}
{"timestamp":"2025-08-12T09:11:40.543Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"f4cb7d4a-e8de-4876-ae99-c0fbcaeffb6c","controller":"reportScheduler","action":"getScheduledReports"}
{"timestamp":"2025-08-12T09:11:40.602Z","level":"INFO","message":"Scheduled reports retrieved successfully","environment":"DEVELOPMENT","requestId":"f4cb7d4a-e8de-4876-ae99-c0fbcaeffb6c","userId":111,"count":1}
{"timestamp":"2025-08-12T09:39:13.210Z","level":"INFO","message":"Google Review Excel report generated successfully","environment":"development","fileName":"google_review_report_1754991553029.xlsx","sheets":7}
{"timestamp":"2025-08-12T09:42:36.339Z","level":"INFO","message":"Instagram Service Configuration","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"hasRedirectUri":true,"clientId":"12424462...","redirectUri":"http://localhost:3000/v1/instagram/callback","apiVersion":"v20.0"}
{"timestamp":"2025-08-12T09:42:36.362Z","level":"INFO","message":"Twitter service initialized","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"redirectUri":"http://***********:5173/business-management/twitter/callback","tokenURL":"https://api.x.com/2/oauth2/token","authURL":"https://twitter.com/i/oauth2/authorize"}
{"timestamp":"2025-08-12T09:42:38.569Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-08-12T04:12:39.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-08-12T09:43:37.159Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"b6c03c6c-f468-4de8-8edf-63378cfcf909","controller":"reportScheduler","action":"runReportNow"}
{"timestamp":"2025-08-12T09:43:37.198Z","level":"INFO","message":"Starting report execution","environment":"DEVELOPMENT","reportId":1,"isManualTrigger":true}
{"timestamp":"2025-08-12T09:43:37.265Z","level":"INFO","message":"Execution log created successfully","environment":"DEVELOPMENT","executionLogId":10,"scheduledReportId":1}
{"timestamp":"2025-08-12T09:43:37.272Z","level":"INFO","message":"Execution log created","environment":"DEVELOPMENT","reportId":1,"executionLogId":10,"reportName":"My Report - HealthOnUs - Corporate Office Beside pillar No. 1725, Phase 2, Kavuri Hills, Madhapur"}
{"timestamp":"2025-08-12T09:43:37.274Z","level":"INFO","message":"Generating report data","environment":"DEVELOPMENT","reportId":1,"reportType":"Google Review Reports","reportName":"My Report - HealthOnUs - Corporate Office Beside pillar No. 1725, Phase 2, Kavuri Hills, Madhapur"}
{"timestamp":"2025-08-12T09:43:37.295Z","level":"INFO","message":"Fetching real data for report","environment":"DEVELOPMENT","reportType":"Google Review Reports","userId":111,"filters":{},"dateRange":{"fromDate":"2025-07-13","toDate":"2025-08-12"}}
{"timestamp":"2025-08-12T09:43:37.399Z","level":"INFO","message":"Report data generated successfully","environment":"DEVELOPMENT","reportId":1,"dataKeys":["reportName","reportType","dateRange","dateRangeObject","filters","generatedAt","recipientName","totalReviews","averageRating","newReviews","responseRate","averageResponseTime","ratingsVsMonth","reviewsVsReplies","ratingDistribution","reviewVolume","recentReviews","locationPerformance","insights","dashboardUrl","nextReportDate"],"dataSize":1862}
{"timestamp":"2025-08-12T09:43:37.405Z","level":"INFO","message":"Generating report attachments","environment":"DEVELOPMENT","reportType":"Google Review Reports"}
{"timestamp":"2025-08-12T09:43:37.413Z","level":"ERROR","message":"Error generating Google Review Excel report:","environment":"DEVELOPMENT","stack":"TypeError: reportData.averageRating?.toFixed is not a function\n    at ReportGenerationService.createReviewSummarySheet (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\reportGenerationService.js:186:52)\n    at ReportGenerationService.generateGoogleReviewExcelReport (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\reportGenerationService.js:43:18)\n    at ReportGenerationService.generateCompleteReportPackage (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\reportGenerationService.js:866:34)\n    at ReportExecutionService.generateReportAttachments (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\reportExecutionService.js:365:39)\n    at ReportExecutionService.executeReport (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\reportExecutionService.js:59:38)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async runReportNow (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\reportScheduler.controller.js:530:20)"}
{"timestamp":"2025-08-12T09:43:37.415Z","level":"ERROR","message":"Error generating complete report package:","environment":"DEVELOPMENT","stack":"TypeError: reportData.averageRating?.toFixed is not a function\n    at ReportGenerationService.createReviewSummarySheet (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\reportGenerationService.js:186:52)\n    at ReportGenerationService.generateGoogleReviewExcelReport (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\reportGenerationService.js:43:18)\n    at ReportGenerationService.generateCompleteReportPackage (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\reportGenerationService.js:866:34)\n    at ReportExecutionService.generateReportAttachments (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\reportExecutionService.js:365:39)\n    at ReportExecutionService.executeReport (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\reportExecutionService.js:59:38)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async runReportNow (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\reportScheduler.controller.js:530:20)"}
{"timestamp":"2025-08-12T09:43:37.420Z","level":"ERROR","message":"Error generating report attachments:","environment":"DEVELOPMENT","stack":"TypeError: reportData.averageRating?.toFixed is not a function\n    at ReportGenerationService.createReviewSummarySheet (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\reportGenerationService.js:186:52)\n    at ReportGenerationService.generateGoogleReviewExcelReport (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\reportGenerationService.js:43:18)\n    at ReportGenerationService.generateCompleteReportPackage (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\reportGenerationService.js:866:34)\n    at ReportExecutionService.generateReportAttachments (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\reportExecutionService.js:365:39)\n    at ReportExecutionService.executeReport (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\reportExecutionService.js:59:38)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async runReportNow (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\reportScheduler.controller.js:530:20)"}
{"timestamp":"2025-08-12T09:43:37.422Z","level":"ERROR","message":"Report execution failed","environment":"DEVELOPMENT","reportId":1,"executionLogId":10,"error":"reportData.averageRating?.toFixed is not a function","stack":"TypeError: reportData.averageRating?.toFixed is not a function\n    at ReportGenerationService.createReviewSummarySheet (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\reportGenerationService.js:186:52)\n    at ReportGenerationService.generateGoogleReviewExcelReport (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\reportGenerationService.js:43:18)\n    at ReportGenerationService.generateCompleteReportPackage (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\reportGenerationService.js:866:34)\n    at ReportExecutionService.generateReportAttachments (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\reportExecutionService.js:365:39)\n    at ReportExecutionService.executeReport (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\reportExecutionService.js:59:38)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async runReportNow (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\reportScheduler.controller.js:530:20)"}
{"timestamp":"2025-08-12T09:43:37.462Z","level":"INFO","message":"Execution log updated successfully","environment":"DEVELOPMENT","executionLogId":10,"affectedRows":1}
{"timestamp":"2025-08-12T09:43:37.464Z","level":"ERROR","message":"Error running report","environment":"DEVELOPMENT","requestId":"b6c03c6c-f468-4de8-8edf-63378cfcf909","userId":"111","reportId":"1","error":"reportData.averageRating?.toFixed is not a function","stack":"TypeError: reportData.averageRating?.toFixed is not a function\n    at ReportGenerationService.createReviewSummarySheet (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\reportGenerationService.js:186:52)\n    at ReportGenerationService.generateGoogleReviewExcelReport (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\reportGenerationService.js:43:18)\n    at ReportGenerationService.generateCompleteReportPackage (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\reportGenerationService.js:866:34)\n    at ReportExecutionService.generateReportAttachments (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\reportExecutionService.js:365:39)\n    at ReportExecutionService.executeReport (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\reportExecutionService.js:59:38)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async runReportNow (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\reportScheduler.controller.js:530:20)"}
{"timestamp":"2025-08-12T09:43:42.811Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"6bc5615e-ceb1-4d3d-a910-a313dbddef90","controller":"reportScheduler","action":"runReportNow"}
{"timestamp":"2025-08-12T09:43:42.860Z","level":"INFO","message":"Starting report execution","environment":"DEVELOPMENT","reportId":1,"isManualTrigger":true}
{"timestamp":"2025-08-12T09:43:42.928Z","level":"INFO","message":"Execution log created successfully","environment":"DEVELOPMENT","executionLogId":11,"scheduledReportId":1}
{"timestamp":"2025-08-12T09:43:42.930Z","level":"INFO","message":"Execution log created","environment":"DEVELOPMENT","reportId":1,"executionLogId":11,"reportName":"My Report - HealthOnUs - Corporate Office Beside pillar No. 1725, Phase 2, Kavuri Hills, Madhapur"}
{"timestamp":"2025-08-12T09:43:42.932Z","level":"INFO","message":"Generating report data","environment":"DEVELOPMENT","reportId":1,"reportType":"Google Review Reports","reportName":"My Report - HealthOnUs - Corporate Office Beside pillar No. 1725, Phase 2, Kavuri Hills, Madhapur"}
{"timestamp":"2025-08-12T09:43:42.935Z","level":"INFO","message":"Fetching real data for report","environment":"DEVELOPMENT","reportType":"Google Review Reports","userId":111,"filters":{},"dateRange":{"fromDate":"2025-07-13","toDate":"2025-08-12"}}
{"timestamp":"2025-08-12T09:43:43.028Z","level":"INFO","message":"Report data generated successfully","environment":"DEVELOPMENT","reportId":1,"dataKeys":["reportName","reportType","dateRange","dateRangeObject","filters","generatedAt","recipientName","totalReviews","averageRating","newReviews","responseRate","averageResponseTime","ratingsVsMonth","reviewsVsReplies","ratingDistribution","reviewVolume","recentReviews","locationPerformance","insights","dashboardUrl","nextReportDate"],"dataSize":1862}
{"timestamp":"2025-08-12T09:43:43.031Z","level":"INFO","message":"Generating report attachments","environment":"DEVELOPMENT","reportType":"Google Review Reports"}
{"timestamp":"2025-08-12T09:43:43.038Z","level":"ERROR","message":"Error generating Google Review Excel report:","environment":"DEVELOPMENT","stack":"TypeError: reportData.averageRating?.toFixed is not a function\n    at ReportGenerationService.createReviewSummarySheet (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\reportGenerationService.js:186:52)\n    at ReportGenerationService.generateGoogleReviewExcelReport (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\reportGenerationService.js:43:18)\n    at ReportGenerationService.generateCompleteReportPackage (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\reportGenerationService.js:866:34)\n    at ReportExecutionService.generateReportAttachments (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\reportExecutionService.js:365:39)\n    at ReportExecutionService.executeReport (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\reportExecutionService.js:59:38)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async runReportNow (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\reportScheduler.controller.js:530:20)"}
{"timestamp":"2025-08-12T09:43:43.041Z","level":"ERROR","message":"Error generating complete report package:","environment":"DEVELOPMENT","stack":"TypeError: reportData.averageRating?.toFixed is not a function\n    at ReportGenerationService.createReviewSummarySheet (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\reportGenerationService.js:186:52)\n    at ReportGenerationService.generateGoogleReviewExcelReport (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\reportGenerationService.js:43:18)\n    at ReportGenerationService.generateCompleteReportPackage (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\reportGenerationService.js:866:34)\n    at ReportExecutionService.generateReportAttachments (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\reportExecutionService.js:365:39)\n    at ReportExecutionService.executeReport (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\reportExecutionService.js:59:38)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async runReportNow (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\reportScheduler.controller.js:530:20)"}
{"timestamp":"2025-08-12T09:43:43.043Z","level":"ERROR","message":"Error generating report attachments:","environment":"DEVELOPMENT","stack":"TypeError: reportData.averageRating?.toFixed is not a function\n    at ReportGenerationService.createReviewSummarySheet (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\reportGenerationService.js:186:52)\n    at ReportGenerationService.generateGoogleReviewExcelReport (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\reportGenerationService.js:43:18)\n    at ReportGenerationService.generateCompleteReportPackage (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\reportGenerationService.js:866:34)\n    at ReportExecutionService.generateReportAttachments (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\reportExecutionService.js:365:39)\n    at ReportExecutionService.executeReport (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\reportExecutionService.js:59:38)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async runReportNow (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\reportScheduler.controller.js:530:20)"}
{"timestamp":"2025-08-12T09:43:43.045Z","level":"ERROR","message":"Report execution failed","environment":"DEVELOPMENT","reportId":1,"executionLogId":11,"error":"reportData.averageRating?.toFixed is not a function","stack":"TypeError: reportData.averageRating?.toFixed is not a function\n    at ReportGenerationService.createReviewSummarySheet (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\reportGenerationService.js:186:52)\n    at ReportGenerationService.generateGoogleReviewExcelReport (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\reportGenerationService.js:43:18)\n    at ReportGenerationService.generateCompleteReportPackage (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\reportGenerationService.js:866:34)\n    at ReportExecutionService.generateReportAttachments (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\reportExecutionService.js:365:39)\n    at ReportExecutionService.executeReport (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\reportExecutionService.js:59:38)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async runReportNow (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\reportScheduler.controller.js:530:20)"}
{"timestamp":"2025-08-12T09:43:43.077Z","level":"INFO","message":"Execution log updated successfully","environment":"DEVELOPMENT","executionLogId":11,"affectedRows":1}
{"timestamp":"2025-08-12T09:43:43.079Z","level":"ERROR","message":"Error running report","environment":"DEVELOPMENT","requestId":"6bc5615e-ceb1-4d3d-a910-a313dbddef90","userId":"111","reportId":"1","error":"reportData.averageRating?.toFixed is not a function","stack":"TypeError: reportData.averageRating?.toFixed is not a function\n    at ReportGenerationService.createReviewSummarySheet (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\reportGenerationService.js:186:52)\n    at ReportGenerationService.generateGoogleReviewExcelReport (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\reportGenerationService.js:43:18)\n    at ReportGenerationService.generateCompleteReportPackage (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\reportGenerationService.js:866:34)\n    at ReportExecutionService.generateReportAttachments (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\reportExecutionService.js:365:39)\n    at ReportExecutionService.executeReport (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\reportExecutionService.js:59:38)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async runReportNow (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\reportScheduler.controller.js:530:20)"}
{"timestamp":"2025-08-12T09:43:56.015Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"048ebd12-0b1f-4bc4-8e65-11fc27e03909","controller":"reportScheduler","action":"getScheduledReports"}
{"timestamp":"2025-08-12T09:43:56.025Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"8204a79a-54f6-44d4-b4d1-8ac86b07e38c","controller":"reportScheduler","action":"getSchedulerStats"}
{"timestamp":"2025-08-12T09:43:56.191Z","level":"INFO","message":"Scheduled reports retrieved successfully","environment":"DEVELOPMENT","requestId":"048ebd12-0b1f-4bc4-8e65-11fc27e03909","userId":111,"count":1}
{"timestamp":"2025-08-12T09:43:56.553Z","level":"INFO","message":"Retrieved SES sending statistics","environment":"DEVELOPMENT","dataPoints":22}
{"timestamp":"2025-08-12T09:46:58.131Z","level":"ERROR","message":"Error fetching Google Review data:","environment":"development","code":"ECONNREFUSED","fatal":true,"stack":"AggregateError\n    at internalConnectMultiple (node:net:1114:18)\n    at afterConnectMultiple (node:net:1667:5)"}
{"timestamp":"2025-08-12T09:46:58.471Z","level":"INFO","message":"Google Review Excel report generated successfully","environment":"development","fileName":"google_review_report_1754992018262.xlsx","sheets":7}
{"timestamp":"2025-08-12T09:54:43.169Z","level":"INFO","message":"Google Review Excel report generated successfully","environment":"development","fileName":"google_review_report_1754992483061.xlsx","sheets":7}
{"timestamp":"2025-08-12T10:24:00.231Z","level":"ERROR","message":"Error fetching Google Review data:","environment":"development","code":"ECONNREFUSED","fatal":true,"stack":"AggregateError\n    at internalConnectMultiple (node:net:1114:18)\n    at afterConnectMultiple (node:net:1667:5)"}
{"timestamp":"2025-08-12T10:24:00.563Z","level":"INFO","message":"Google Review Excel report generated successfully","environment":"development","fileName":"google_review_report_1754994240384.xlsx","sheets":7}
{"timestamp":"2025-08-12T12:57:38.311Z","level":"INFO","message":"Instagram Service Configuration","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"hasRedirectUri":true,"clientId":"12424462...","redirectUri":"http://localhost:3000/v1/instagram/callback","apiVersion":"v20.0"}
{"timestamp":"2025-08-12T12:57:38.356Z","level":"INFO","message":"Twitter service initialized","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"redirectUri":"http://***********:5173/business-management/twitter/callback","tokenURL":"https://api.x.com/2/oauth2/token","authURL":"https://twitter.com/i/oauth2/authorize"}
{"timestamp":"2025-08-12T12:57:48.161Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-08-12T12:57:48.322Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"8d20250d-f3f2-4578-aab4-d355b5ae89bd","controller":"reportScheduler","action":"getSchedulerStats"}
{"timestamp":"2025-08-12T12:57:48.334Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"aac3c094-f568-41dc-bbf4-8ee6fd7b148b","controller":"reportScheduler","action":"getScheduledReports"}
{"timestamp":"2025-08-12T07:27:48.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-08-12T12:57:48.618Z","level":"INFO","message":"Scheduled reports retrieved successfully","environment":"DEVELOPMENT","requestId":"aac3c094-f568-41dc-bbf4-8ee6fd7b148b","userId":111,"count":9}
{"timestamp":"2025-08-12T12:57:48.940Z","level":"INFO","message":"Retrieved SES sending statistics","environment":"DEVELOPMENT","dataPoints":23}
{"timestamp":"2025-08-12T12:57:54.570Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"37a798d5-4782-4c10-96bd-6638adb2fe1a","controller":"reportScheduler","action":"runReportNow"}
{"timestamp":"2025-08-12T12:57:54.599Z","level":"INFO","message":"Starting report execution","environment":"DEVELOPMENT","reportId":1,"isManualTrigger":true}
{"timestamp":"2025-08-12T12:57:54.656Z","level":"INFO","message":"Execution log created successfully","environment":"DEVELOPMENT","executionLogId":13,"scheduledReportId":1}
{"timestamp":"2025-08-12T12:57:54.658Z","level":"INFO","message":"Execution log created","environment":"DEVELOPMENT","reportId":1,"executionLogId":13,"reportName":"My Report - HealthOnUs - Corporate Office Beside pillar No. 1725, Phase 2, Kavuri Hills, Madhapur"}
{"timestamp":"2025-08-12T12:57:54.660Z","level":"INFO","message":"Generating report data","environment":"DEVELOPMENT","reportId":1,"reportType":"Google Review Reports","reportName":"My Report - HealthOnUs - Corporate Office Beside pillar No. 1725, Phase 2, Kavuri Hills, Madhapur"}
{"timestamp":"2025-08-12T12:57:54.669Z","level":"INFO","message":"Fetching real data for report","environment":"DEVELOPMENT","reportType":"Google Review Reports","userId":111,"filters":{},"dateRange":{"fromDate":"2025-07-13","toDate":"2025-08-12"}}
{"timestamp":"2025-08-12T12:57:54.762Z","level":"INFO","message":"Report data generated successfully","environment":"DEVELOPMENT","reportId":1,"dataKeys":["reportName","reportType","dateRange","dateRangeObject","filters","generatedAt","recipientName","businessName","accountName","locationsLabel","totalReviews","averageRating","newReviews","responseRate","averageResponseTime","ratingsVsMonth","reviewsVsReplies","ratingDistribution","reviewVolume","reviews","recentReviews","locationPerformance","insights","dashboardUrl","nextReportDate"],"dataSize":17395}
{"timestamp":"2025-08-12T12:57:54.765Z","level":"INFO","message":"Generating report attachments","environment":"DEVELOPMENT","reportType":"Google Review Reports"}
{"timestamp":"2025-08-12T12:57:54.847Z","level":"INFO","message":"Google Review Excel report generated successfully","environment":"DEVELOPMENT","fileName":"google_review_report_1755003474775.xlsx","sheets":7}
{"timestamp":"2025-08-12T12:57:54.851Z","level":"INFO","message":"Uploading file to S3","environment":"DEVELOPMENT","s3Key":"reports/2025/8/google_review_report_1755003474775.xlsx","mimeType":"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","bucketName":"gmb-social-assets"}
{"timestamp":"2025-08-12T12:57:57.982Z","level":"INFO","message":"File uploaded successfully to S3","environment":"DEVELOPMENT","s3Key":"reports/2025/8/google_review_report_1755003474775.xlsx","location":"https://gmb-social-assets.s3.amazonaws.com/reports/2025/8/google_review_report_1755003474775.xlsx","etag":"\"c19b5c215cc3da20446fff278243edda\""}
{"timestamp":"2025-08-12T12:57:57.985Z","level":"INFO","message":"Report uploaded to S3 successfully","environment":"DEVELOPMENT","fileName":"google_review_report_1755003474775.xlsx","s3Key":"reports/2025/8/google_review_report_1755003474775.xlsx","url":"https://gmb-social-assets.s3.amazonaws.com/reports/2025/8/google_review_report_1755003474775.xlsx"}
{"timestamp":"2025-08-12T12:57:57.987Z","level":"INFO","message":"Complete report package generated successfully","environment":"DEVELOPMENT","reportType":"Google Review Reports","hasExcel":true,"hasPDF":false,"attachments":1}
{"timestamp":"2025-08-12T12:57:57.989Z","level":"INFO","message":"Parsing S3 URL","environment":"DEVELOPMENT","url":"https://gmb-social-assets.s3.amazonaws.com/reports/2025/8/google_review_report_1755003474775.xlsx"}
{"timestamp":"2025-08-12T12:57:57.990Z","level":"INFO","message":"Downloading file from S3","environment":"DEVELOPMENT","bucket":"gmb-social-assets","key":"reports/2025/8/google_review_report_1755003474775.xlsx","url":"https://gmb-social-assets.s3.amazonaws.com/reports/2025/8/google_review_report_1755003474775.xlsx"}
{"timestamp":"2025-08-12T12:58:00.705Z","level":"INFO","message":"Successfully downloaded file from S3","environment":"DEVELOPMENT","bucket":"gmb-social-assets","key":"reports/2025/8/google_review_report_1755003474775.xlsx","size":14756,"contentType":"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}
{"timestamp":"2025-08-12T12:58:00.706Z","level":"INFO","message":"Report attachments generated successfully","environment":"DEVELOPMENT","reportType":"Google Review Reports","attachmentCount":1}
{"timestamp":"2025-08-12T12:58:00.707Z","level":"INFO","message":"Generating email content","environment":"DEVELOPMENT","reportType":"Google Review Reports"}
{"timestamp":"2025-08-12T12:58:00.720Z","level":"INFO","message":"Base email template loaded successfully","environment":"DEVELOPMENT"}
{"timestamp":"2025-08-12T12:58:00.722Z","level":"INFO","message":"Handlebars helpers registered successfully","environment":"DEVELOPMENT"}
{"timestamp":"2025-08-12T12:58:00.723Z","level":"INFO","message":"Email template service initialized successfully","environment":"DEVELOPMENT"}
{"timestamp":"2025-08-12T12:58:00.732Z","level":"INFO","message":"Template 'googleReviewReports' loaded and compiled successfully","environment":"DEVELOPMENT"}
{"timestamp":"2025-08-12T12:58:00.756Z","level":"INFO","message":"Template 'googleReviewReports' rendered successfully","environment":"DEVELOPMENT","dataKeys":["subject","recipientName","reportName","dateRange","generatedDate","locationCount","totalReviews","averageRating","newReviews","responseRate","averageResponseTime","ratingTrend","ratingChange","newReviewsTrend","newReviewsChange","responseRateTrend","responseRateChange","responseTimeTrend","responseTimeChange","ratingDistribution","recentReviews","locationPerformance","insights","hasRecentReviews","hasAttachments","attachments","dashboardUrl","manageReviewsUrl","nextReportDate"]}
{"timestamp":"2025-08-12T12:58:00.758Z","level":"INFO","message":"Email content generated successfully","environment":"DEVELOPMENT","reportType":"Google Review Reports","htmlSize":23646}
{"timestamp":"2025-08-12T12:58:00.759Z","level":"INFO","message":"Uploading file to S3","environment":"DEVELOPMENT","s3Key":"report-attachments/2025-08-12/13/google_review_report_1755003474775.xlsx","mimeType":"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","bucketName":"gmb-social-assets"}
{"timestamp":"2025-08-12T12:58:03.786Z","level":"INFO","message":"File uploaded successfully to S3","environment":"DEVELOPMENT","s3Key":"report-attachments/2025-08-12/13/google_review_report_1755003474775.xlsx","location":"https://gmb-social-assets.s3.amazonaws.com/report-attachments/2025-08-12/13/google_review_report_1755003474775.xlsx","etag":"\"c19b5c215cc3da20446fff278243edda\""}
{"timestamp":"2025-08-12T12:58:03.788Z","level":"INFO","message":"Report attachment stored successfully","environment":"DEVELOPMENT","executionLogId":13,"fileName":"google_review_report_1755003474775.xlsx","s3Key":"report-attachments/2025-08-12/13/google_review_report_1755003474775.xlsx","size":14756}
{"timestamp":"2025-08-12T12:58:03.823Z","level":"INFO","message":"Execution log updated successfully","environment":"DEVELOPMENT","executionLogId":13,"affectedRows":1}
{"timestamp":"2025-08-12T12:58:03.824Z","level":"INFO","message":"Report attachments processing completed","environment":"DEVELOPMENT","executionLogId":13,"totalAttachments":1,"successfulUploads":1}
{"timestamp":"2025-08-12T12:58:03.826Z","level":"INFO","message":"Sending report emails","environment":"DEVELOPMENT","toRecipients":1,"ccRecipients":0,"bccRecipients":0,"attachments":1}
{"timestamp":"2025-08-12T12:58:03.827Z","level":"INFO","message":"Downloading attachment from S3","environment":"DEVELOPMENT","fileName":"google_review_report_1755003474775.xlsx","url":"https://gmb-social-assets.s3.amazonaws.com/report-attachments/2025-08-12/13/google_review_report_1755003474775.xlsx"}
{"timestamp":"2025-08-12T12:58:03.828Z","level":"INFO","message":"Parsing S3 URL","environment":"DEVELOPMENT","url":"https://gmb-social-assets.s3.amazonaws.com/report-attachments/2025-08-12/13/google_review_report_1755003474775.xlsx"}
{"timestamp":"2025-08-12T12:58:03.830Z","level":"INFO","message":"Downloading file from S3","environment":"DEVELOPMENT","bucket":"gmb-social-assets","key":"report-attachments/2025-08-12/13/google_review_report_1755003474775.xlsx","url":"https://gmb-social-assets.s3.amazonaws.com/report-attachments/2025-08-12/13/google_review_report_1755003474775.xlsx"}
{"timestamp":"2025-08-12T12:58:06.542Z","level":"INFO","message":"Successfully downloaded file from S3","environment":"DEVELOPMENT","bucket":"gmb-social-assets","key":"report-attachments/2025-08-12/13/google_review_report_1755003474775.xlsx","size":14756,"contentType":"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}
{"timestamp":"2025-08-12T12:58:06.544Z","level":"INFO","message":"Successfully prepared attachment","environment":"DEVELOPMENT","fileName":"google_review_report_1755003474775.xlsx","size":14756}
{"timestamp":"2025-08-12T12:58:06.545Z","level":"INFO","message":"Prepared email attachments","environment":"DEVELOPMENT","totalAttachments":1,"attachmentSizes":[{"name":"google_review_report_1755003474775.xlsx","size":14756,"type":"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}]}
{"timestamp":"2025-08-12T12:58:06.548Z","level":"INFO","message":"Preparing to send email","environment":"DEVELOPMENT","to":1,"cc":0,"bcc":0,"subject":"Google Review Reports - My Report - HealthOnUs - Corporate Office Beside pillar No. 1725, Phase 2, K","attachments":1}
{"timestamp":"2025-08-12T12:58:06.872Z","level":"INFO","message":"Email sent successfully","environment":"DEVELOPMENT","messageIds":["010901989e5c02ff-1e7c8d70-3968-4c1b-bdae-06f999ae6c0e-000000"],"to":1,"cc":0,"bcc":0,"attachments":1}
{"timestamp":"2025-08-12T12:58:06.903Z","level":"INFO","message":"Email transaction logged successfully","environment":"DEVELOPMENT","transactionLogId":11,"executionLogId":13,"recipientEmail":"<EMAIL>","emailStatus":"sent"}
{"timestamp":"2025-08-12T12:58:06.904Z","level":"INFO","message":"Email transactions batch logged successfully","environment":"DEVELOPMENT","executionLogId":13,"transactionCount":1,"transactionIds":[11]}
{"timestamp":"2025-08-12T12:58:06.906Z","level":"INFO","message":"Report emails sent successfully","environment":"DEVELOPMENT","emailsSent":1,"messageIds":["010901989e5c02ff-1e7c8d70-3968-4c1b-bdae-06f999ae6c0e-000000"]}
{"timestamp":"2025-08-12T12:58:06.936Z","level":"INFO","message":"Execution log updated successfully","environment":"DEVELOPMENT","executionLogId":13,"affectedRows":1}
{"timestamp":"2025-08-12T12:58:06.937Z","level":"INFO","message":"Report execution completed successfully","environment":"DEVELOPMENT","reportId":1,"executionLogId":13,"emailsSent":1,"executionDuration":12308,"attachmentCount":1}
{"timestamp":"2025-08-12T12:58:06.938Z","level":"INFO","message":"Manual report execution completed","environment":"DEVELOPMENT","requestId":"37a798d5-4782-4c10-96bd-6638adb2fe1a","userId":111,"reportId":1,"executionId":13,"emailsSent":1}
{"timestamp":"2025-08-12T12:58:06.967Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"4d78c96c-c531-4dd2-b39f-e4100df79576","controller":"reportScheduler","action":"getScheduledReports"}
{"timestamp":"2025-08-12T12:58:07.014Z","level":"INFO","message":"Scheduled reports retrieved successfully","environment":"DEVELOPMENT","requestId":"4d78c96c-c531-4dd2-b39f-e4100df79576","userId":111,"count":9}
{"timestamp":"2025-08-12T13:29:50.836Z","level":"INFO","message":"Instagram Service Configuration","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"hasRedirectUri":true,"clientId":"12424462...","redirectUri":"http://localhost:3000/v1/instagram/callback","apiVersion":"v20.0"}
{"timestamp":"2025-08-12T13:29:50.863Z","level":"INFO","message":"Twitter service initialized","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"redirectUri":"http://***********:5173/business-management/twitter/callback","tokenURL":"https://api.x.com/2/oauth2/token","authURL":"https://twitter.com/i/oauth2/authorize"}
{"timestamp":"2025-08-12T13:29:52.433Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-08-12T07:59:52.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-08-12T13:30:26.816Z","level":"INFO","message":"Fetching location metrics from database","environment":"DEVELOPMENT","locationId":"11768998853595948830","accountId":"115421225674922028914","startDate":"2024-08-12","endDate":"2025-08-12"}
{"timestamp":"2025-08-12T13:30:26.927Z","level":"INFO","message":"Analytics data found in database","environment":"DEVELOPMENT","locationId":"11768998853595948830","metricsCount":7}
{"timestamp":"2025-08-12T13:30:27.621Z","level":"INFO","message":"Fetching location metrics from database","environment":"DEVELOPMENT","locationId":"11768998853595948830","accountId":"115421225674922028914","startDate":"2024-08-12","endDate":"2025-08-12"}
{"timestamp":"2025-08-12T13:30:27.710Z","level":"INFO","message":"Analytics data found in database","environment":"DEVELOPMENT","locationId":"11768998853595948830","metricsCount":7}
{"timestamp":"2025-08-12T13:30:31.294Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"5ffb54f1-5c1f-4088-b04a-89272af5cfb8","controller":"business","action":"businessList","userId":"111"}
{"timestamp":"2025-08-12T13:30:31.296Z","level":"INFO","message":"Fetching business list","environment":"DEVELOPMENT","requestId":"5ffb54f1-5c1f-4088-b04a-89272af5cfb8","userId":"111"}
{"timestamp":"2025-08-12T13:30:31.303Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"d179acfc-6e24-42bf-8668-9d6b1cc76eba","controller":"accounts","action":"AccountsList"}
{"timestamp":"2025-08-12T13:30:31.606Z","level":"INFO","message":"Business list fetched successfully","environment":"DEVELOPMENT","requestId":"5ffb54f1-5c1f-4088-b04a-89272af5cfb8","userId":"111","businessCount":1}
{"timestamp":"2025-08-12T13:30:39.859Z","level":"INFO","message":"Fetching multi-location analytics from database","environment":"DEVELOPMENT","locationIds":["11768998853595948830","12582277252923211675","14168200124020302600","15696979104240226809","16301288520536997474","3235303985985579046","3570874142987199482","9351894999656607285"],"startDate":"2024-08-12","endDate":"2025-08-12","locationCount":8}
{"timestamp":"2025-08-12T13:30:39.966Z","level":"INFO","message":"Multi-location analytics query results","environment":"DEVELOPMENT","totalRequested":8,"foundInDatabase":4,"missingFromDatabase":4,"locationsWithoutData":["12582277252923211675","14168200124020302600","15696979104240226809","9351894999656607285"]}
{"timestamp":"2025-08-12T13:31:31.999Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"2603018b-6046-4f52-8092-ce56b0e3558b","controller":"business","action":"businessList","userId":"111"}
{"timestamp":"2025-08-12T13:31:32.002Z","level":"INFO","message":"Fetching business list","environment":"DEVELOPMENT","requestId":"2603018b-6046-4f52-8092-ce56b0e3558b","userId":"111"}
{"timestamp":"2025-08-12T13:31:32.018Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"32f039cf-bb36-45f8-9b0d-39110327881e","controller":"accounts","action":"AccountsList"}
{"timestamp":"2025-08-12T13:31:32.048Z","level":"INFO","message":"Business list fetched successfully","environment":"DEVELOPMENT","requestId":"2603018b-6046-4f52-8092-ce56b0e3558b","userId":"111","businessCount":1}
{"timestamp":"2025-08-12T13:31:32.066Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"ee6ae50e-7f96-4c31-ac0d-69251c171f46","controller":"business","action":"businessList","userId":"111"}
{"timestamp":"2025-08-12T13:31:32.069Z","level":"INFO","message":"Fetching business list","environment":"DEVELOPMENT","requestId":"ee6ae50e-7f96-4c31-ac0d-69251c171f46","userId":"111"}
{"timestamp":"2025-08-12T13:31:32.122Z","level":"INFO","message":"Business list fetched successfully","environment":"DEVELOPMENT","requestId":"ee6ae50e-7f96-4c31-ac0d-69251c171f46","userId":"111","businessCount":1}
{"timestamp":"2025-08-12T13:31:33.578Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"e9182c27-69e5-4d33-be55-c4510c03a02d","controller":"accounts","action":"AccountsList"}
{"timestamp":"2025-08-12T13:32:11.510Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"36fc6c17-5eaf-4ead-aeaa-f68588b41693","controller":"business","action":"businessList","userId":"111"}
{"timestamp":"2025-08-12T13:32:11.512Z","level":"INFO","message":"Fetching business list","environment":"DEVELOPMENT","requestId":"36fc6c17-5eaf-4ead-aeaa-f68588b41693","userId":"111"}
{"timestamp":"2025-08-12T13:32:11.520Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"17ee19b3-1248-4f34-ad77-dcc19de35c7f","controller":"accounts","action":"AccountsList"}
{"timestamp":"2025-08-12T13:32:11.571Z","level":"INFO","message":"Business list fetched successfully","environment":"DEVELOPMENT","requestId":"36fc6c17-5eaf-4ead-aeaa-f68588b41693","userId":"111","businessCount":1}
{"timestamp":"2025-08-12T13:32:11.585Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"75e3c96d-35ce-4863-80e3-15019cbdbb60","controller":"business","action":"businessList","userId":"111"}
{"timestamp":"2025-08-12T13:32:11.587Z","level":"INFO","message":"Fetching business list","environment":"DEVELOPMENT","requestId":"75e3c96d-35ce-4863-80e3-15019cbdbb60","userId":"111"}
{"timestamp":"2025-08-12T13:32:11.635Z","level":"INFO","message":"Business list fetched successfully","environment":"DEVELOPMENT","requestId":"75e3c96d-35ce-4863-80e3-15019cbdbb60","userId":"111","businessCount":1}
{"timestamp":"2025-08-12T13:32:12.875Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"06ae6733-461c-465a-8bac-2d8489bfacf7","controller":"accounts","action":"AccountsList"}
{"timestamp":"2025-08-12T13:32:35.172Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"97ab8326-6f15-41a6-8c74-a8d66f33829a","controller":"business","action":"businessList","userId":"111"}
{"timestamp":"2025-08-12T13:32:35.174Z","level":"INFO","message":"Fetching business list","environment":"DEVELOPMENT","requestId":"97ab8326-6f15-41a6-8c74-a8d66f33829a","userId":"111"}
{"timestamp":"2025-08-12T13:32:35.177Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"0ca98756-1ebf-495f-beb0-80f39fa236c7","controller":"accounts","action":"AccountsList"}
{"timestamp":"2025-08-12T13:32:35.221Z","level":"INFO","message":"Business list fetched successfully","environment":"DEVELOPMENT","requestId":"97ab8326-6f15-41a6-8c74-a8d66f33829a","userId":"111","businessCount":1}
{"timestamp":"2025-08-12T13:32:35.234Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"35a2de2b-e750-4759-a614-3ca01b9f76e5","controller":"business","action":"businessList","userId":"111"}
{"timestamp":"2025-08-12T13:32:35.235Z","level":"INFO","message":"Fetching business list","environment":"DEVELOPMENT","requestId":"35a2de2b-e750-4759-a614-3ca01b9f76e5","userId":"111"}
{"timestamp":"2025-08-12T13:32:35.290Z","level":"INFO","message":"Business list fetched successfully","environment":"DEVELOPMENT","requestId":"35a2de2b-e750-4759-a614-3ca01b9f76e5","userId":"111","businessCount":1}
{"timestamp":"2025-08-12T13:32:36.644Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"820567e2-a2ae-441e-b0cc-944a3a5d85cb","controller":"accounts","action":"AccountsList"}
{"timestamp":"2025-08-12T13:35:47.538Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"95e6a6c0-e2b4-48eb-97d1-34f237ef0dba","controller":"accounts","action":"AccountsList"}
{"timestamp":"2025-08-12T13:35:47.580Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"f6cbf420-67ae-4f60-989c-c6f263e2c921","controller":"business","action":"businessList","userId":"111"}
{"timestamp":"2025-08-12T13:35:47.586Z","level":"INFO","message":"Fetching business list","environment":"DEVELOPMENT","requestId":"f6cbf420-67ae-4f60-989c-c6f263e2c921","userId":"111"}
{"timestamp":"2025-08-12T13:35:47.642Z","level":"INFO","message":"Business list fetched successfully","environment":"DEVELOPMENT","requestId":"f6cbf420-67ae-4f60-989c-c6f263e2c921","userId":"111","businessCount":1}
{"timestamp":"2025-08-12T13:35:52.810Z","level":"INFO","message":"Fetching multi-location analytics from database","environment":"DEVELOPMENT","locationIds":["11768998853595948830","12582277252923211675","14168200124020302600","15696979104240226809","16301288520536997474","3235303985985579046","3570874142987199482","9351894999656607285"],"startDate":"2024-08-12","endDate":"2025-08-12","locationCount":8}
{"timestamp":"2025-08-12T13:35:52.923Z","level":"INFO","message":"Multi-location analytics query results","environment":"DEVELOPMENT","totalRequested":8,"foundInDatabase":4,"missingFromDatabase":4,"locationsWithoutData":["12582277252923211675","14168200124020302600","15696979104240226809","9351894999656607285"]}
{"timestamp":"2025-08-12T13:35:57.439Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"5681c872-bc23-4d9e-9404-7991a3b87f62","controller":"business","action":"businessList","userId":"111"}
{"timestamp":"2025-08-12T13:35:57.441Z","level":"INFO","message":"Fetching business list","environment":"DEVELOPMENT","requestId":"5681c872-bc23-4d9e-9404-7991a3b87f62","userId":"111"}
{"timestamp":"2025-08-12T13:35:57.454Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"da53c4a6-27a2-4572-8217-be279c302661","controller":"accounts","action":"AccountsList"}
{"timestamp":"2025-08-12T13:35:57.497Z","level":"INFO","message":"Business list fetched successfully","environment":"DEVELOPMENT","requestId":"5681c872-bc23-4d9e-9404-7991a3b87f62","userId":"111","businessCount":1}
{"timestamp":"2025-08-12T13:35:57.520Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"f05147d8-95ba-4263-a170-e2641a7af942","controller":"business","action":"businessList","userId":"111"}
{"timestamp":"2025-08-12T13:35:57.523Z","level":"INFO","message":"Fetching business list","environment":"DEVELOPMENT","requestId":"f05147d8-95ba-4263-a170-e2641a7af942","userId":"111"}
{"timestamp":"2025-08-12T13:35:57.580Z","level":"INFO","message":"Business list fetched successfully","environment":"DEVELOPMENT","requestId":"f05147d8-95ba-4263-a170-e2641a7af942","userId":"111","businessCount":1}
{"timestamp":"2025-08-12T13:35:59.139Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"4a70b241-2c44-4098-b020-9a71d8bb6b6f","controller":"accounts","action":"AccountsList"}
